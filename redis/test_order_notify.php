<?php
/**
 * 订单完成通知测试脚本
 * 用于测试推送功能是否正常工作
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "=== 订单完成通知系统测试 ===\n\n";

// 测试1: 检查推送函数是否可用
echo "1. 测试推送函数...\n";
if (function_exists('tuisong')) {
    echo "✅ 推送函数存在\n";
} else {
    echo "❌ 推送函数不存在\n";
    exit(1);
}

// 测试2: 检查数据库连接
echo "\n2. 测试数据库连接...\n";
try {
    $test_query = $DB->prepare_query("SELECT 1", []);
    if ($test_query) {
        echo "✅ 数据库连接正常\n";
        $test_query->close();
    } else {
        echo "❌ 数据库连接失败\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ 数据库连接异常: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试3: 检查Redis连接
echo "\n3. 测试Redis连接...\n";
try {
    $redis = new Redis();
    if ($redis->connect("127.0.0.1", "6379")) {
        if ($redis->ping()) {
            echo "✅ Redis连接正常\n";
        } else {
            echo "❌ Redis ping失败\n";
        }
    } else {
        echo "❌ Redis连接失败\n";
    }
} catch (Exception $e) {
    echo "❌ Redis连接异常: " . $e->getMessage() . "\n";
}

// 测试4: 查询测试订单
echo "\n4. 查询最近的订单...\n";
try {
    $sql = "SELECT o.*, u.name as user_name, u.user as user_account 
            FROM qingka_wangke_order o 
            LEFT JOIN qingka_wangke_user u ON o.uid = u.uid 
            WHERE o.status IN ('已完成', '已退款', '已取消', '异常')
            ORDER BY o.oid DESC 
            LIMIT 5";
    
    $stmt = $DB->prepare_query($sql, []);
    if ($stmt) {
        $result = $stmt->get_result();
        $orders = [];
        while ($order = $result->fetch_assoc()) {
            $orders[] = $order;
        }
        $stmt->close();
        
        if (count($orders) > 0) {
            echo "✅ 找到 " . count($orders) . " 个测试订单\n";
            foreach ($orders as $order) {
                echo "   - 订单ID: {$order['oid']}, 状态: {$order['status']}, 用户: {$order['user_name']}\n";
            }
        } else {
            echo "⚠️ 没有找到合适的测试订单\n";
        }
    } else {
        echo "❌ 查询订单失败\n";
    }
} catch (Exception $e) {
    echo "❌ 查询订单异常: " . $e->getMessage() . "\n";
}

// 测试5: 检查用户推送设置
echo "\n5. 检查用户推送设置...\n";
try {
    $sql = "SELECT uid, name, user, tuisongtoken, user_settings FROM qingka_wangke_user WHERE tuisongtoken != '' LIMIT 5";
    $stmt = $DB->prepare_query($sql, []);
    if ($stmt) {
        $result = $stmt->get_result();
        $users = [];
        while ($user = $result->fetch_assoc()) {
            $users[] = $user;
        }
        $stmt->close();
        
        if (count($users) > 0) {
            echo "✅ 找到 " . count($users) . " 个配置了推送的用户\n";
            foreach ($users as $user) {
                $settings = json_decode($user['user_settings'], true);
                $order_notify = $settings['order_completion_notification'] ?? 0;
                echo "   - 用户: {$user['name']}({$user['user']}), 订单通知: " . ($order_notify ? "开启" : "关闭") . "\n";
            }
        } else {
            echo "⚠️ 没有找到配置推送的用户\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 检查用户设置异常: " . $e->getMessage() . "\n";
}

// 测试6: 模拟推送消息格式
echo "\n6. 测试推送消息格式...\n";
$test_order = [
    'oid' => '12345',
    'ptname' => '智慧树',
    'school' => '清华大学',
    'user' => 'test001',
    'kcname' => '高等数学',
    'status' => '已完成',
    'process' => '100%',
    'remarks' => '学习完成',
    'fees' => '15.00'
];

$status_info = ['icon' => '✅', 'color' => 'success', 'desc' => '学习任务已完成'];

$message = "{$status_info['icon']} 订单状态更新\n";
$message .= "━━━━━━━━━━━━━━━━━━━━\n";
$message .= "📦 订单ID：{$test_order['oid']}\n";
$message .= "🏷️ 平台：{$test_order['ptname']}\n";
$message .= "🏫 学校：{$test_order['school']}\n";
$message .= "👤 账号：{$test_order['user']}\n";
$message .= "📚 课程：{$test_order['kcname']}\n";
$message .= "📊 状态：{$status_info['desc']}\n";
$message .= "📈 进度：{$test_order['process']}\n";
$message .= "📝 备注：{$test_order['remarks']}\n";
$message .= "💰 费用：{$test_order['fees']}元\n";
$message .= "⏰ 更新时间：" . date('Y-m-d H:i:s') . "\n";
$message .= "━━━━━━━━━━━━━━━━━━━━\n";
$message .= "🎉 恭喜！您的学习任务已完成！";

echo "✅ 推送消息格式测试:\n";
echo "---\n";
echo $message . "\n";
echo "---\n";

// 测试7: 检查脚本文件权限
echo "\n7. 检查脚本文件权限...\n";
$script_file = dirname(__FILE__) . '/order_completion_notify.php';
if (file_exists($script_file)) {
    if (is_readable($script_file)) {
        echo "✅ 主脚本文件可读\n";
    } else {
        echo "❌ 主脚本文件不可读\n";
    }
    
    if (is_executable($script_file)) {
        echo "✅ 主脚本文件可执行\n";
    } else {
        echo "⚠️ 主脚本文件不可执行（可能需要设置权限）\n";
    }
} else {
    echo "❌ 主脚本文件不存在\n";
}

echo "\n=== 测试完成 ===\n";
echo "如果所有测试都通过，可以设置宝塔计划任务了。\n";
echo "建议的计划任务命令: /usr/bin/php " . dirname(__FILE__) . "/order_completion_notify.php\n";
echo "建议的执行频率: 每2分钟\n";
?>
