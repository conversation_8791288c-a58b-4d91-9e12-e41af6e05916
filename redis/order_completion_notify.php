<?php
/**
 * 订单完成通知脚本
 * 用于监控订单状态变化并发送推送通知
 * 
 * 功能特点：
 * 1. 监控订单状态变化（已完成、已退款、已取消等）
 * 2. 发送详细的推送通知给用户
 * 3. 防重复通知机制
 * 4. 详细的日志记录
 * 5. 支持多种订单状态
 * 
 * 建议执行频率：每2分钟执行一次
 * 宝塔计划任务设置：每2分钟执行 /usr/bin/php /www/wwwroot/117.72.158.75/redis/order_completion_notify.php
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');
include(dirname(__FILE__) . '/notification_config.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

$log_file = dirname(__FILE__) . '/order_completion_notify.log';
$max_log_size = 10 * 1024 * 1024; // 10MB

// 日志函数
function writeLog($message) {
    global $log_file, $max_log_size;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}\n";
    
    // 检查日志文件大小，如果超过限制则清空
    if (file_exists($log_file) && filesize($log_file) > $max_log_size) {
        file_put_contents($log_file, '');
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    echo $log_message;
}

// 获取订单状态的中文描述和图标
function getStatusInfo($status) {
    $status_map = [
        '已完成' => ['icon' => '✅', 'color' => 'success', 'desc' => '学习任务已完成'],
        '已退款' => ['icon' => '💰', 'color' => 'warning', 'desc' => '订单已退款'],
        '已取消' => ['icon' => '❌', 'color' => 'danger', 'desc' => '订单已取消'],
        '异常' => ['icon' => '⚠️', 'color' => 'danger', 'desc' => '订单处理异常'],
        '售后完成' => ['icon' => '🔧', 'color' => 'info', 'desc' => '售后处理完成'],
        '人工处理' => ['icon' => '👨‍💼', 'color' => 'warning', 'desc' => '转人工处理'],
        '已暂停' => ['icon' => '⏸️', 'color' => 'warning', 'desc' => '订单已暂停'],
        '已停止' => ['icon' => '⏹️', 'color' => 'danger', 'desc' => '订单已停止']
    ];
    
    return $status_map[$status] ?? ['icon' => '📋', 'color' => 'info', 'desc' => $status];
}

// 格式化推送消息
function formatNotificationMessage($order, $status_info) {
    $message = "{$status_info['icon']} 订单状态更新\n";
    $message .= "━━━━━━━━━━━━━━━━━━━━\n";
    $message .= "📦 订单ID：{$order['oid']}\n";
    $message .= "🏷️ 平台：{$order['ptname']}\n";
    $message .= "🏫 学校：{$order['school']}\n";
    $message .= "👤 账号：{$order['user']}\n";
    $message .= "📚 课程：{$order['kcname']}\n";
    $message .= "📊 状态：{$status_info['desc']}\n";
    
    // 如果有进度信息，显示进度
    if (!empty($order['process']) && $order['process'] !== '0' && $order['process'] !== '0%') {
        $message .= "📈 进度：{$order['process']}\n";
    }
    
    // 如果有备注信息，显示备注（支持多行显示）
    if (!empty($order['remarks'])) {
        $remarks_text = trim($order['remarks']);
        if (mb_strlen($remarks_text) <= 50) {
            // 短备注直接显示
            $message .= "📝 备注：{$remarks_text}\n";
        } else {
            // 长备注分行显示
            $message .= "📝 备注：\n";
            $message .= "   " . mb_substr($remarks_text, 0, 50) . "\n";
            if (mb_strlen($remarks_text) > 50) {
                $remaining = mb_substr($remarks_text, 50);
                if (mb_strlen($remaining) <= 50) {
                    $message .= "   " . $remaining . "\n";
                } else {
                    $message .= "   " . mb_substr($remaining, 0, 50) . "...\n";
                }
            }
        }
    }
    
    // 显示费用信息
    if ($order['fees'] > 0) {
        $message .= "💰 费用：{$order['fees']}元\n";
    }
    
    $message .= "⏰ 更新时间：" . date('Y-m-d H:i:s') . "\n";
    $message .= "━━━━━━━━━━━━━━━━━━━━\n";
    
    // 根据状态添加不同的结尾
    switch ($order['status']) {
        case '已完成':
            $message .= "🎉 恭喜！您的学习任务已完成！";
            break;
        case '已退款':
            $message .= "💰 订单费用已退回您的账户！";
            break;
        case '已取消':
            $message .= "❌ 订单已取消，如有疑问请联系客服！";
            break;
        case '异常':
            $message .= "⚠️ 订单处理异常，请联系客服处理！";
            break;
        default:
            $message .= "📋 订单状态已更新，请及时查看！";
    }
    
    return $message;
}

// 发送订单完成通知
function sendOrderNotification($order) {
    global $DB;

    try {
        // 获取状态信息
        $status_info = getStatusInfo($order['status']);

        // 格式化推送消息
        $message = formatNotificationMessage($order, $status_info);

        // 发送推送通知
        $result = tuisong($order['uid'], "order_completion", "订单状态更新", $message);

        writeLog("订单 {$order['oid']} 推送通知: {$result}");

        return true;

    } catch (Exception $e) {
        writeLog("发送订单 {$order['oid']} 通知失败: " . $e->getMessage());
        return false;
    }
}

// 发送合并通知
function sendBatchNotification($user_orders) {
    global $DB;

    try {
        $uid = $user_orders[0]['uid'];
        $total_orders = count($user_orders);

        // 按状态分组统计
        $status_groups = [];
        foreach ($user_orders as $order) {
            $status = $order['status'];
            if (!isset($status_groups[$status])) {
                $status_groups[$status] = [];
            }
            $status_groups[$status][] = $order;
        }

        // 构建合并消息
        $message = "📦 批量订单状态更新\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "📊 本次更新：{$total_orders}个订单\n\n";

        $batch_config = getBatchDisplayConfig();
        $max_detail = $batch_config['max_detail_orders'];

        foreach ($status_groups as $status => $orders) {
            $status_info = getStatusInfo($status);
            $count = count($orders);
            $message .= "{$status_info['icon']} {$status}：{$count}个订单\n";

            // 显示配置数量的订单详情
            $show_count = min($max_detail, $count);
            for ($i = 0; $i < $show_count; $i++) {
                $order = $orders[$i];
                $kcname = mb_substr($order['kcname'], 0, 20); // 限制课程名长度
                if (mb_strlen($order['kcname']) > 20) {
                    $kcname .= "...";
                }
                $message .= "   • 订单{$order['oid']}：{$kcname}\n";
            }

            // 如果超过配置数量，显示省略信息
            if ($count > $max_detail) {
                $message .= "   • 还有" . ($count - $max_detail) . "个订单...\n";
            }
            $message .= "\n";
        }

        $message .= "⏰ 更新时间：" . date('Y-m-d H:i:s') . "\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "请登录系统查看详细信息！";

        // 发送推送通知
        $result = tuisong($uid, "order_completion", "批量订单状态更新", $message);

        writeLog("用户 {$uid} 批量通知({$total_orders}个订单): {$result}");

        return true;

    } catch (Exception $e) {
        writeLog("发送批量通知失败: " . $e->getMessage());
        return false;
    }
}

// 主执行逻辑
function main() {
    global $DB;
    
    writeLog("开始执行订单完成通知检查...");
    
    try {
        // 需要通知的订单状态
        $notify_statuses = ['已完成', '已退款', '已取消', '异常', '售后完成', '人工处理', '已暂停', '已停止'];
        $status_placeholders = str_repeat('?,', count($notify_statuses) - 1) . '?';
        
        // 查询最近更新的订单（最近10分钟内更新的）
        $sql = "SELECT o.*, u.name as user_name, u.user as user_account 
                FROM qingka_wangke_order o 
                LEFT JOIN qingka_wangke_user u ON o.uid = u.uid 
                WHERE o.status IN ({$status_placeholders})
                AND o.updatetime >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                AND o.updatetime IS NOT NULL
                ORDER BY o.updatetime DESC 
                LIMIT 100";
        
        $stmt = $DB->prepare_query($sql, $notify_statuses);
        
        if (!$stmt) {
            writeLog("查询订单失败: " . $DB->error());
            return;
        }
        
        $result = $stmt->get_result();
        $notification_count = 0;
        $processed_orders = [];
        $user_orders = []; // 按用户分组的订单

        // 收集所有需要通知的订单
        while ($order = $result->fetch_assoc()) {
            // 检查是否已经发送过通知（防重复）
            $notification_key = "order_notify_{$order['oid']}_{$order['status']}_" . date('Y-m-d', strtotime($order['updatetime']));

            // 使用Redis检查是否已发送过通知
            $redis = new Redis();
            if ($redis->connect("127.0.0.1", "6379")) {
                if ($redis->exists($notification_key)) {
                    continue; // 已发送过通知，跳过
                }
            }

            // 按用户ID分组
            $uid = $order['uid'];
            if (!isset($user_orders[$uid])) {
                $user_orders[$uid] = [];
            }
            $user_orders[$uid][] = $order;
            $processed_orders[] = $order['oid'];

            // 避免处理过多订单，限制每次最多50个
            if (count($processed_orders) >= 50) {
                break;
            }
        }

        // 智能发送通知
        foreach ($user_orders as $uid => $orders) {
            $order_count = count($orders);

            // 检查用户通知数量限制
            if (!checkPerformanceLimits($notification_count, 'notifications')) {
                writeLog("⚠️ 已达到单次通知数量限制，停止处理");
                break;
            }

            if ($order_count == getNotificationConfig('merge_rules.single_order', 1)) {
                // 单个订单：发送详细通知
                if (sendOrderNotification($orders[0])) {
                    $notification_count++;
                    recordNotificationStats('单条通知', 1, $uid);
                }
            } elseif (shouldSendIndividually($order_count)) {
                // 少量订单：发送逐条通知（用户可能关注每个订单）
                foreach ($orders as $order) {
                    if (sendOrderNotification($order)) {
                        $notification_count++;
                    }
                    usleep(getNotificationInterval('single')); // 配置化间隔
                }
                recordNotificationStats('逐条通知', $order_count, $uid);
            } else {
                // 大量订单：发送合并通知
                if (sendBatchNotification($orders)) {
                    $notification_count++;
                    recordNotificationStats('合并通知', $order_count, $uid);
                }
            }

            // 标记所有订单已发送通知
            foreach ($orders as $order) {
                $notification_key = generateNotificationKey($order['oid'], $order['status'], date('Y-m-d', strtotime($order['updatetime'])));
                if ($redis->ping()) {
                    $redis->setex($notification_key, getCacheDuration(), 1);
                }
            }

            // 添加批量通知间隔
            if ($order_count > 1) {
                usleep(getNotificationInterval('batch'));
            }
        }
        
        $stmt->close();
        
        if ($notification_count > 0) {
            writeLog("本次共发送 {$notification_count} 个订单完成通知，订单ID: " . implode(', ', $processed_orders));
        } else {
            writeLog("没有需要发送通知的订单");
        }
        
    } catch (Exception $e) {
        writeLog("执行过程中发生错误: " . $e->getMessage());
    }
    
    writeLog("订单完成通知检查完成");
}

// 执行主逻辑
main();
?>
