<?php
include('confing/common.php');
$php_Self = substr($_SERVER['PHP_SELF'], strripos($_SERVER['PHP_SELF'], "/") + 1);
if ($php_Self != "mqgd.php") {
    $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
    $msg = urldecode($msg);
    exit(json_encode(['code' => -1, 'msg' => $msg]));
}
$supertoken = $DB->get_row("SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`='1'");
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;
@header('Content-Type: application/json; charset=UTF-8');
switch ($act) {
    case 'addTicket':
    $content = trim(strip_tags($_GET['content']));
    if (empty($content)) {
        exit('{"code":-1,"msg":"问题内容不能为空"}');
    }
    if (strlen($content) > 500 ) {
        exit('{"code":-1,"msg":"字数超过限制，问题请尽量简短"}');
    }
    $title = "无";
    $region = "其他问题";
    $date = date('Y-m-d H:i:s');
    $Content = $date . " 用户提问: " . $content;
    $sql = "INSERT INTO `qingka_wangke_gongdan` (`title`, `region`, `content`, `uid`, `state`, `addtime`) VALUES (?, ?, ?, ?, ?, ?)";
    $params = [$title, $region, $Content, $userrow['uid'], '待回复', $date];
    if ($DB->prepare_query($sql, $params)) {
        // 获取用户信息
        $user_info = $DB->prepare_getrow("SELECT name, user FROM qingka_wangke_user WHERE uid = ?", [$userrow['uid']]);
        $user_name = $user_info['name'] ?? '未知用户';
        $user_account = $user_info['user'] ?? '未知账号';

        // 格式化推送消息
        $message = "📋 新工单提醒\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "👤 提交用户：{$user_name}({$user_account})\n";
        $message .= "🏷️ 工单类型：{$region}\n";
        // 显示问题内容（支持多行显示）
        $content_text = trim($content);
        if (mb_strlen($content_text) <= 60) {
            // 短内容直接显示
            $message .= "📝 问题内容：{$content_text}\n";
        } else {
            // 长内容分行显示
            $message .= "📝 问题内容：\n";
            $message .= "   " . mb_substr($content_text, 0, 60) . "\n";
            if (mb_strlen($content_text) > 60) {
                $remaining = mb_substr($content_text, 60);
                if (mb_strlen($remaining) <= 60) {
                    $message .= "   " . $remaining . "\n";
                } else {
                    $message .= "   " . mb_substr($remaining, 0, 60) . "...\n";
                }
            }
        }
        $message .= "⏰ 提交时间：{$date}\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "请及时处理用户反馈！";

        // 防止重复推送：如果上级就是超级管理员，只发一次
        if ($userrow['uuid'] == '1') {
            // 上级是超级管理员，只发给超级管理员
            tuisong('1', "work_order", "新工单通知", $message);
        } else {
            // 上级不是超级管理员，发给超级管理员和上级
            tuisong('1', "work_order", "新工单通知", $message);
            tuisong($userrow['uuid'], "work_order", "新工单通知", $message);
        }
        exit('{"code":1,"msg":"工单新增成功"}');
    } else {
        exit('{"code":-1,"msg":"工单新增失败"}');
    }
    break;

    
case 'feedback':
    $oid = intval($_GET['oid']);
    $feedback = trim(strip_tags($_GET['feedback']));
    if (empty($oid) || empty($feedback)) {
        echo json_encode(['code' => 0, 'msg' => '订单ID或反馈内容不能为空']);
        exit;
    }
    if (strlen($feedback) > 500 || preg_match('/[0-9]/', $feedback)) {
        echo json_encode(['code' => 0, 'msg' => '反馈内容不能超过100个字，且不能包含数字和字母']);
        exit;
    }

    $sql = "SELECT * FROM `qingka_wangke_order` WHERE `oid` = ?";
    $row = $DB->prepare_getrow($sql, [$oid]);

    $date = date('Y-m-d H:i:s');
    if ($row) {
        $title = $row['ptname'] . "\n" . $row['school'] . ' ' . $row['user'] . ' ' . $row['pass'] . "\n" . $row['kcname']."\n状态: " . $row['status']." 备注: " . $row['remarks']."\n下单时间: " . $row['addtime'];
        $title = addslashes($title);
        $region = $oid;
        $content = $date . " 用户反馈: ".$feedback;

        $checkQuery = "SELECT COUNT(*) FROM `qingka_wangke_gongdan` WHERE `title` = ?";
        $count = $DB->prepare_count($checkQuery, [$title]);

        if ($count > 0) {
            echo json_encode(['code' => 0, 'msg' => '该工单已存在']);
            exit;
        }

        $usertoken_sql = "SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`=?";
        $usertoken = $DB->prepare_getrow($usertoken_sql, [$row['uid']]);
        $hasToken = !empty($usertoken['tuisongtoken']);

        $insertQuery = "INSERT INTO `qingka_wangke_gongdan` (`title`, `region`, `content`, `uid`, `state`, `addtime`) VALUES (?, ?, ?, ?, '待回复', ?)";
        $insertResult = $DB->prepare_query($insertQuery, [$title, $region, $content, $userrow['uid'], $date]);

        if ($insertResult) {
            // 获取用户信息
            $user_info = $DB->prepare_getrow("SELECT name, user FROM qingka_wangke_user WHERE uid = ?", [$userrow['uid']]);
            $user_name = $user_info['name'] ?? '未知用户';
            $user_account = $user_info['user'] ?? '未知账号';

            // 格式化推送消息
            $message = "📋 订单反馈工单\n";
            $message .= "━━━━━━━━━━━━━━━━━━━━\n";
            $message .= "👤 反馈用户：{$user_name}({$user_account})\n";
            $message .= "📦 订单ID：{$oid}\n";
            $message .= "🏷️ 所属平台：{$row['ptname']}\n";
            $message .= "🏫 学校账号：{$row['school']} {$row['user']}\n";
            $message .= "🔑 登录密码：{$row['pass']}\n";
            $message .= "📚 课程名称：{$row['kcname']}\n";
            $message .= "📊 订单状态：{$row['status']}\n";

            // 显示进度信息（如果有）
            if (!empty($row['process']) && $row['process'] !== '0' && $row['process'] !== '0%') {
                $message .= "📈 学习进度：{$row['process']}\n";
            }

            // 显示费用信息
            if (!empty($row['fees']) && $row['fees'] > 0) {
                $message .= "💰 订单费用：{$row['fees']}元\n";
            }

            // 显示备注信息（如果有）
            if (!empty($row['remarks'])) {
                $remarks = mb_substr($row['remarks'], 0, 40);
                if (mb_strlen($row['remarks']) > 40) {
                    $remarks .= "...";
                }
                $message .= "📝 订单备注：{$remarks}\n";
            }

            // 显示反馈内容（支持多行显示）
            $feedback_content = trim($feedback);
            if (mb_strlen($feedback_content) <= 60) {
                // 短内容直接显示
                $message .= "💬 反馈内容：{$feedback_content}\n";
            } else {
                // 长内容分行显示
                $message .= "💬 反馈内容：\n";
                $message .= "   " . mb_substr($feedback_content, 0, 60) . "\n";
                if (mb_strlen($feedback_content) > 60) {
                    $remaining = mb_substr($feedback_content, 60);
                    if (mb_strlen($remaining) <= 60) {
                        $message .= "   " . $remaining . "\n";
                    } else {
                        $message .= "   " . mb_substr($remaining, 0, 60) . "...\n";
                    }
                }
            }
            $message .= "⏰ 反馈时间：{$date}\n";
            $message .= "━━━━━━━━━━━━━━━━━━━━\n";
            $message .= "请及时处理订单问题！";

            // 防止重复推送：如果上级就是超级管理员，只发一次
            if ($userrow['uuid'] == '1') {
                // 上级是超级管理员，只发给超级管理员
                tuisong('1', "work_order", "订单反馈通知", $message);
            } else {
                // 上级不是超级管理员，发给超级管理员和上级
                tuisong('1', "work_order", "订单反馈通知", $message);
                tuisong($userrow['uuid'], "work_order", "订单反馈通知", $message);
            }
            if ($hasToken) {
                echo json_encode(['code' => 1, 'msg' => '反馈成功']);
            } else {
                echo json_encode(['code' => 1, 'msg' => '未绑定推送token！请前往推送系统绑定推送token以获取最新回复。']);
            }
        } else {
            echo json_encode(['code' => 0, 'msg' => '反馈失败，请重试']);
        }
    } else {
        echo json_encode(['code' => 0, 'msg' => '订单不存在']);
    }
    break;
    
    case 'gdlist':
        $searchQuery = trim($_POST['searchQuery']);
        $statusFilter = trim($_POST['statusFilter']);
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;
        $validStatuses = ['待回复', '已回复', '已完成'];

        $sqlCondition = $userrow['uid'] != '1' ? "WHERE uid = ?" : "WHERE 1=1";
        $params = $userrow['uid'] != '1' ? [$userrow['uid']] : [];

        if (!empty($searchQuery)) {
            $sqlCondition .= " AND (title LIKE ? OR region LIKE ?)";
            $params[] = "%" . $searchQuery . "%";
            $params[] = "%" . $searchQuery . "%";
        }

        if (!empty($statusFilter) && in_array($statusFilter, $validStatuses)) {
            $sqlCondition .= " AND state = ?";
            $params[] = $statusFilter;
        } elseif (empty($statusFilter) && empty($searchQuery) && $userrow['uid'] == 1) {
            $sqlCondition .= " AND state = '待回复'";
        }

        $totalSql = "SELECT COUNT(*) FROM qingka_wangke_gongdan " . $sqlCondition;
        $total = $DB->prepare_count($totalSql, $params);

        $dataSql = "SELECT * FROM qingka_wangke_gongdan " . $sqlCondition . " ORDER BY gid DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        $stmt = $DB->prepare_query($dataSql, $params);
        $result = $stmt->get_result();

        $data = [];
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
        $stmt->close();

        $responseData = ['code' => 1, 'data' => $data, 'total' => $total];
        exit(json_encode($responseData));
        break;

    case 'getPushToken':
        $sql = "SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`=?";
        $token = $DB->prepare_getrow($sql, [$userrow['uid']]);
        if ($token) {
            exit(json_encode(['code' => 1, 'token' => $token['tuisongtoken']]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '暂未设置推送token']));
        }
        break;

    case 'shan':
        $gid = trim($_POST['gid']);
        $sql = "select * from qingka_wangke_gongdan where gid=?";
        $b = $DB->prepare_getrow($sql, [$gid]);
        if ($userrow['uid'] != $b['uid'] && $userrow['uid'] != '1') {
            exit('{"code":-1,"msg":"该工单不是你的！无法删除！"}');
        }
        $sql = "delete from qingka_wangke_gongdan where gid=?";
        $DB->prepare_query($sql, [$gid]);
        exit('{"code":1,"msg":"删除成功！"}');
        break;

    case 'answer':
        $gid = trim($_POST['gid']);
        $answer = trim($_POST['answer']);
        $sql = "select * from qingka_wangke_gongdan where gid=?";
        $b = $DB->prepare_getrow($sql, [$gid]);
        if ($userrow['uid'] != '1') {
            exit('{"code":-1,"msg":"无权限！"}');
        }
        $newAnswer = $b['content'] . "\n\n" . $date . " 管理员回复: " . $answer;
        $sql = "update qingka_wangke_gongdan set `content`=?,`state`='已回复' where gid=?";
        $DB->prepare_query($sql, [$newAnswer, $gid]);
        $sql = "SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`=?";
        $token = $DB->prepare_getrow($sql, [$b['uid']]);

        // 格式化工单回复推送消息
        $message = "💬 工单回复通知\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "🏷️ 工单类型：{$b['region']}\n";
        // 显示管理员回复（支持多行显示）
        $answer_text = trim($answer);
        if (mb_strlen($answer_text) <= 60) {
            // 短内容直接显示
            $message .= "💬 管理员回复：{$answer_text}\n";
        } else {
            // 长内容分行显示
            $message .= "💬 管理员回复：\n";
            $message .= "   " . mb_substr($answer_text, 0, 60) . "\n";
            if (mb_strlen($answer_text) > 60) {
                $remaining = mb_substr($answer_text, 60);
                if (mb_strlen($remaining) <= 60) {
                    $message .= "   " . $remaining . "\n";
                } else {
                    $message .= "   " . mb_substr($remaining, 0, 60) . "...\n";
                }
            }
        }
        $message .= "⏰ 回复时间：{$date}\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "请及时查看工单详情！";

        tuisong($b['uid'], "work_order", "工单回复通知", $message);
        exit('{"code":1,"msg":"回复成功！"}');
        break;

    case 'bohui':
        $gid = trim($_POST['gid']);
        $answer = trim($_POST['answer']);
        $sql = "select * from qingka_wangke_gongdan where gid=?";
        $b = $DB->prepare_getrow($sql, [$gid]);
        if ($userrow['uid'] != '1') {
            exit('{"code":-1,"msg":"无权限！"}');
        }
        $newAnswer = $b['content'] . "\n\n" . $date . " 管理员修改工单状态为已完成，回复: " . $answer;
        $sql = "update qingka_wangke_gongdan set `content`=?,`state`='已完成' where gid=?";
        $DB->prepare_query($sql, [$newAnswer, $gid]);
        $sql = "SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`=?";
        $token = $DB->prepare_getrow($sql, [$b['uid']]);

        // 格式化工单完成推送消息
        $message = "✅ 工单完成通知\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "🏷️ 工单类型：{$b['region']}\n";
        // 显示完成说明（支持多行显示）
        $answer_text = trim($answer);
        if (mb_strlen($answer_text) <= 60) {
            // 短内容直接显示
            $message .= "💬 完成说明：{$answer_text}\n";
        } else {
            // 长内容分行显示
            $message .= "💬 完成说明：\n";
            $message .= "   " . mb_substr($answer_text, 0, 60) . "\n";
            if (mb_strlen($answer_text) > 60) {
                $remaining = mb_substr($answer_text, 60);
                if (mb_strlen($remaining) <= 60) {
                    $message .= "   " . $remaining . "\n";
                } else {
                    $message .= "   " . mb_substr($remaining, 0, 60) . "...\n";
                }
            }
        }
        $message .= "⏰ 完成时间：{$date}\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "您的问题已处理完成！";

        tuisong($b['uid'], "work_order", "工单完成通知", $message);
        exit('{"code":1,"msg":"该订单已处理完成"}');
        break;
        
     case 'toanswer':
     $gid = trim($_POST['gid']);
     $toanswer = trim($_POST['toanswer']);
     $sql = "select * from qingka_wangke_gongdan where gid=?";
     $b = $DB->prepare_getrow($sql, [$gid]);
    if (strlen($toanswer) > 100 ) {
        echo json_encode(['code' => 0, 'msg' => '问题不能超过100个字']);
        exit;
    }
    if ($b['state'] == '已完成') {
        exit('{"code":-1,"msg":"工单已完成，无法再追问"}');
    } else {
        // 获取用户信息
        $user_info = $DB->prepare_getrow("SELECT name, user FROM qingka_wangke_user WHERE uid = ?", [$userrow['uid']]);
        $user_name = $user_info['name'] ?? '未知用户';
        $user_account = $user_info['user'] ?? '未知账号';

        // 格式化工单追问推送消息
        $message = "❓ 工单追问通知\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "👤 追问用户：{$user_name}({$user_account})\n";
        $message .= "🏷️ 工单类型：{$b['region']}\n";
        // 显示追问内容（支持多行显示）
        $toanswer_text = trim($toanswer);
        if (mb_strlen($toanswer_text) <= 60) {
            // 短内容直接显示
            $message .= "💬 追问内容：{$toanswer_text}\n";
        } else {
            // 长内容分行显示
            $message .= "💬 追问内容：\n";
            $message .= "   " . mb_substr($toanswer_text, 0, 60) . "\n";
            if (mb_strlen($toanswer_text) > 60) {
                $remaining = mb_substr($toanswer_text, 60);
                if (mb_strlen($remaining) <= 60) {
                    $message .= "   " . $remaining . "\n";
                } else {
                    $message .= "   " . mb_substr($remaining, 0, 60) . "...\n";
                }
            }
        }
        $message .= "⏰ 追问时间：{$date}\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
        $message .= "用户催促处理进展，请尽快回复！";

        tuisong('1', "work_order", "工单追问通知", $message);
        tuisong($userrow['uuid'], "work_order", "工单追问通知", $message);
        $newContent = $b['content'] . "\n\n" . $date . " 用户追问: " . $toanswer;
        $sql = "update qingka_wangke_gongdan set `content`=?,`state`='待回复' where gid=?";
        $DB->prepare_query($sql, [$newContent, $gid]);
        exit('{"code":1,"msg":"二次提问完成"}');
    }
    break;
    
}
?>