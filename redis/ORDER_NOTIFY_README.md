# 订单完成通知系统

## 功能介绍

订单完成通知系统是一个智能化的推送服务，用于监控订单状态变化并及时通知用户。

### 主要功能

1. **实时监控订单状态变化**
   - 已完成 ✅
   - 已退款 💰
   - 已取消 ❌
   - 异常 ⚠️
   - 售后完成 🔧
   - 人工处理 👨‍💼
   - 已暂停 ⏸️
   - 已停止 ⏹️

2. **智能通知策略**
   - **单个订单**：发送详细通知
   - **少量订单(2-5个)**：逐条发送，间隔0.5秒
   - **大量订单(6个以上)**：发送合并通知
   - 避免通知轰炸，提升用户体验

3. **详细的推送消息**
   - 订单基本信息（ID、平台、学校、账号、课程）
   - 状态变化详情
   - 进度信息（如有）
   - 备注信息（如有）
   - 费用信息
   - 更新时间

4. **防重复通知机制**
   - 使用Redis缓存防止重复推送
   - 每个订单每种状态每天只推送一次

5. **完善的日志记录**
   - 详细的执行日志
   - 通知统计信息
   - 自动日志轮转（10MB限制）

6. **可配置的通知规则**
   - 支持自定义合并阈值
   - 可调整通知间隔时间
   - 灵活的性能限制设置

## 安装配置

### 1. 宝塔计划任务设置

在宝塔面板中添加计划任务：

**任务类型**: Shell脚本
**任务名称**: 订单完成通知
**执行周期**: 每2分钟
**脚本内容**:
```bash
/usr/bin/php /www/wwwroot/117.72.158.75/redis/order_completion_notify.php
```

**Cron表达式**: `*/2 * * * *` (每2分钟执行一次)

### 2. 手动执行测试

```bash
cd /www/wwwroot/117.72.158.75/redis
php order_completion_notify.php
```

### 3. 查看日志

```bash
tail -f /www/wwwroot/117.72.158.75/redis/order_completion_notify.log
```

## 推送设置

用户可以在系统中的"推送设置"页面开启或关闭订单完成通知：

1. 登录系统
2. 进入"推送设置"页面
3. 开启"订单完成通知"开关
4. 保存设置

## 推送消息格式示例

```
✅ 订单状态更新
━━━━━━━━━━━━━━━━━━━━
📦 订单ID：12345
🏷️ 平台：智慧树
🏫 学校：清华大学
👤 账号：student001
📚 课程：高等数学
📊 状态：学习任务已完成
📈 进度：100%
💰 费用：15.00元
⏰ 更新时间：2025-08-26 15:30:00
━━━━━━━━━━━━━━━━━━━━
🎉 恭喜！您的学习任务已完成！
```

## 技术特点

1. **高效执行**: 每次最多处理50个订单，避免系统负载过高
2. **智能过滤**: 只处理最近10分钟内更新的订单
3. **状态缓存**: 使用Redis防止重复推送
4. **错误处理**: 完善的异常处理机制
5. **日志管理**: 自动日志轮转，避免日志文件过大

## 监控建议

1. **定期检查日志**: 确保脚本正常执行
2. **监控推送成功率**: 关注推送失败的情况
3. **Redis连接状态**: 确保Redis服务正常运行
4. **数据库性能**: 监控查询性能，必要时优化索引

## 故障排除

### 常见问题

1. **推送失败**
   - 检查用户是否设置了推送Token
   - 检查用户是否开启了订单完成通知
   - 检查网络连接是否正常

2. **重复推送**
   - 检查Redis服务是否正常
   - 检查缓存键是否正确设置

3. **脚本执行失败**
   - 检查PHP路径是否正确
   - 检查文件权限是否正确
   - 检查数据库连接是否正常

### 调试模式

可以在脚本中添加调试输出来排查问题：

```php
// 在main()函数开始处添加
writeLog("调试: 开始查询订单，状态: " . implode(', ', $notify_statuses));
```

## 更新日志

### v1.0.0 (2025-08-26)
- 初始版本发布
- 支持8种订单状态通知
- 实现防重复推送机制
- 添加详细的推送消息格式
- 完善的日志记录功能

## 注意事项

1. **执行频率**: 建议每2分钟执行一次，不要过于频繁
2. **系统负载**: 脚本会自动限制处理数量，避免系统负载过高
3. **数据库索引**: 确保`updatetime`字段有索引以提高查询性能
4. **Redis配置**: 确保Redis有足够的内存存储缓存数据
5. **推送限制**: ShowDoc推送服务可能有频率限制，注意控制推送频率
