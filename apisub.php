<?php
include('confing/common.php'); 
include('confing/permission.php');
$redis=new Redis();
$redis->connect("127.0.0.1","6379");
$post_data = json_decode(file_get_contents('php://input'), true);
if (!is_array($post_data)) {
    $post_data = $_POST;
}
    $php_Self = substr($_SERVER['PHP_SELF'],strripos($_SERVER['PHP_SELF'],"/")+1);
    if($php_Self!="apisub.php"){
        $msg = '%E6%96%87%E4%BB%B6%E9%94%99%E8%AF%AF';
        $msg = urldecode($msg);
       exit(json_encode(['code' => -1, 'msg' => $msg])); }
    switch($act){
    //公告模块
    case 'gonggaolist':
    $searchQuery = trim($_POST['searchQuery']);
    $statusFilter = trim($_POST['statusFilter']);
    $addtime1 = trim($_POST['addtime']);
    $addtime2 = trim($_POST['addtime']);
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
    $offset = ($page - 1) * $limit;
    $params = [];
    $sql1 = "WHERE 1=1";
    if ($userrow['uid'] != '1') {
        $sql1 = "WHERE uid=?";
        $params[] = $userrow['uid'];
    }
    if (!empty($searchQuery)) {
        $sql1 .= " AND (title LIKE ? OR content LIKE ?)";
        $params[] = "%" . $searchQuery . "%";
        $params[] = "%" . $searchQuery . "%";
    }
    if (!empty($statusFilter)) {
        $sql1 .= " AND status=?";
        $params[] = $statusFilter;
    }
    if ($addtime1 != '' && $addtime2 != '') {
        $sql1 .= " AND time > ? AND time < ?";
        $params[] = $addtime1;
        $params[] = $addtime2;
    }
    $total = $DB->prepare_count("SELECT COUNT(*) FROM qingka_wangke_gonggao {$sql1}", $params);
    $sql = "SELECT * FROM qingka_wangke_gonggao {$sql1} ORDER BY id DESC LIMIT {$limit} OFFSET {$offset}";
    $a = $DB->prepare_query($sql, $params);
    $data = [];
    if ($a) {
        $result = $a->get_result();
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
        $a->close();
    }
    $responseData = array('code' => 1, 'data' => $data, 'total' => $total);
    exit(json_encode($responseData));
    break;
    
case 'gonggao':
    $title = strip_tags($_POST['title']);
    $content = strip_tags($_POST['content'], '<br>');
    $status = $_POST['status'];
    $zhiding = $_POST['zhiding'];
    $type = $_POST['type'];
    $id = $_POST['id'];
    if (empty($title)) {
        exit('{"code":-1,"msg":"标题不能为空"}');
    }
    if (empty($content)) {
        exit('{"code":-1,"msg":"公告内容不能为空"}');
    }
    if (strlen($content) > 1000) {
        exit('{"code":-1,"msg":"字数超过限制，内容请尽量简短"}');
    }
    $date = date('Y-m-d H:i:s');
    if ($type == 'add') {
        $sql = "INSERT INTO `qingka_wangke_gonggao` (`title`, `content`, `time`, `uid`, `status`, `zhiding`) VALUES (?, ?, ?, ?, ?, ?)";
        $params = [$title, $content, $date, $userrow['uid'], '1', $zhiding];
    } else if ($type == 'edit') {
        $sql = "UPDATE `qingka_wangke_gonggao` SET `title` = ?, `content` = ?, `status` = ?, `zhiding` = ? WHERE `id` = ?";
        $params = [$title, $content, $status, $zhiding, $id];
    } else {
        exit('{"code":-1,"msg":"无效的操作类型"}');
    }
    if ($DB->prepare_query($sql, $params)) {
        exit('{"code":1,"msg":"公告' . ($type == 'add' ? '新增' : '修改') . '成功"}');
    } else {
        exit('{"code":-1,"msg":"公告' . ($type == 'add' ? '新增' : '修改') . '失败"}');
    }
    break;
    
    case 'gonggaoshan':
        $id = trim($_POST['id']);
        $sql = "select * from qingka_wangke_gonggao where id=?";
        $b = $DB->prepare_getrow($sql, [$id]);
        if ($userrow['uid'] != $b['uid'] && $userrow['uid'] != '1') {
            exit('{"code":-1,"msg":"该公告不是你的！无法删除！"}');
        }
        $sql = "delete from qingka_wangke_gonggao where id=?";
        $DB->prepare_query($sql, [$id]);
        exit('{"code":1,"msg":"删除成功！"}');
        break;
        
case 'showgonggao':
    $uuid = $userrow['uuid'];
    $data = [];
    $sql = "SELECT id, zhiding, title, content, time, uid FROM qingka_wangke_gonggao WHERE status = 1 AND (uid = ? OR uid = 1) ORDER BY zhiding DESC, time DESC";
    $params = [$uuid];
    $stmt = $DB->prepare_query($sql, $params);
    if ($stmt) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'id' => $row['id'],
                'zhiding' => $row['zhiding'],
                'title' => $row['title'],
                'content' => $row['content'],
                'shenfen' => ($row['uid'] == 1) ? '全站通知' : '上级通知',
                'time' => $row['time'],
            ];
        }
        $stmt->close();
    }
    $data = ['code' => 1, 'data' => $data];
    exit(json_encode($data));
    break;
    
    //数据中心模块
    case 'moneylog':
    $date = $_POST['date'];
    if (empty($date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        exit(json_encode(['code' => 0, 'msg' => 'Invalid date format']));
    }
    $start_time = $date . ' 00:00:00';
    $end_time = $date . ' 23:59:59';
    $sql_conditions = "addtime BETWEEN ? AND ? AND CAST(money AS DECIMAL(10,2)) < 0";
    $params = [$start_time, $end_time];
    if ($userrow['uid'] != '1') {
        $sql_conditions .= " AND uid=?";
        $params[] = $userrow['uid'];
    }
    $sql = "SELECT type, SUM(CAST(money AS DECIMAL(10,2))) AS usemoney 
            FROM qingka_wangke_log 
            WHERE {$sql_conditions} 
            GROUP BY type 
            ORDER BY type ASC";
    
    $stmt = $DB->prepare_query($sql, $params);
    if ($stmt === false) {
        exit(json_encode(['code' => 0, 'msg' => 'Database query failed']));
    }
    
    $result = $stmt->get_result();
    $data = [];
    while ($row = $DB->fetch($result)) {
        $data[] = [
            'type' => $row['type'],
            'usemoney' => $row['usemoney']
        ];
    }
    $stmt->close();
    $response = [
        'code' => 1,
        'msg' => '查询成功',
        'data' => $data
    ];
    
    exit(json_encode($response));
break;
	
	case 'orderlog':
    $date = $_POST['date'];
    if (empty($date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        exit(json_encode(['code' => 0, 'msg' => 'Invalid date format']));
    }
    $start_time = $date . ' 00:00:00';
    $end_time = $date . ' 23:59:59';
    $sql_conditions = "addtime BETWEEN ? AND ?";
    $params = [$start_time, $end_time];
    if ($userrow['uid'] != '1') {
        $sql_conditions .= " AND uid=?";
        $params[] = $userrow['uid'];
    }
    $sql = "SELECT ptname, COUNT(*) AS order_count 
            FROM qingka_wangke_order 
            WHERE {$sql_conditions} 
            GROUP BY ptname 
            ORDER BY ptname ASC";
    
    $stmt = $DB->prepare_query($sql, $params);
    if ($stmt === false) {
        exit(json_encode(['code' => 0, 'msg' => 'Database query failed']));
    }
    
    $result = $stmt->get_result();
    $data = [];
    while ($row = $DB->fetch($result)) {
        $data[] = [
            'ptname' => $row['ptname'],
            'order_count' => (int)$row['order_count']
        ];
    }
    $stmt->close();
    $response = [
        'code' => 1,
        'msg' => '查询成功',
        'data' => $data
    ];
    
    exit(json_encode($response));
break;
    //订单提交模块
    case 'getclassfl':
    $fenlei = trim($_POST['id']);
    $fenleixinxi = $DB->prepare_getrow("SELECT text FROM qingka_wangke_fenlei WHERE id = ?", [$fenlei]);
    $where = "status=1";
    $params = [];
    if ($fenlei != '') {
        if ($fenlei == '99') {
            $uid = $userrow['uid'];
            $cids = $redis->sMembers("user_favorites:{$uid}");
            if (!empty($cids)) {
                $cids_str = implode(',', $cids);
                $where .= " AND cid IN ($cids_str)";
            } else {
                $data = [];
                exit(json_encode(['code' => 1, 'data' => $data]));
            }
        } else {
            $where .= " AND fenlei=?";
            $params[] = $fenlei;
        }
    }
    $sql = "SELECT sort,cid,name,price,content,fenlei FROM qingka_wangke_class WHERE {$where} ORDER BY cid ASC";
    $stmt = $DB->prepare_query($sql, $params);
    $data = [];
    if ($stmt) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            if ($row['yunsuan'] == "*") {
                $price = $row['price'] * $userrow['addprice'];
            } elseif ($row['yunsuan'] == "+") {
                $price = round($row['price'] + $userrow['addprice'], 2);
            } else {
                $price = round($row['price'] * $userrow['addprice'], 2);
            }
            
            // 保存初始计算的价格作为 price1
            $price1 = $price;
            
            // 处理密价
            $mijia = $DB->prepare_getrow("SELECT price,mode FROM qingka_wangke_mijia WHERE uid=? AND cid=?", [$userrow['uid'], $row['cid']]);
            $mijia_price = $price; // 默认等于当前 price
            if ($mijia) {
                if ($mijia['mode'] == 0) {
                    $mijia_price = round($price - $mijia['price'], 2);
                } elseif ($mijia['mode'] == 1) {
                    $mijia_price = round(($row['price'] - $mijia['price']) * $userrow['addprice'], 2);
                } elseif ($mijia['mode'] == 2) {
                    $mijia_price = $mijia['price'];
                }
                if ($mijia_price <= 0) {
                    $mijia_price = 0;
                }
            }
            
            // 处理质押价格
            $zhiya_price = $price;
            $zhiya = $DB->get_row("SELECT zr.*, zc.discount_rate
                FROM qingka_wangke_zhiya_records zr
                LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id
                WHERE zr.uid='{$userrow['uid']}'
                AND zc.category_id='{$row['fenlei']}'
                AND zr.status=1
                ORDER BY zr.id DESC LIMIT 1");
            if($zhiya && $zhiya['discount_rate'] > 0) {
                $zhiya_price = round($price * $zhiya['discount_rate'], 2);
            }
            
            // 比较密价和质押价格，选择较低的价格
            if($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
                if($mijia_price <= $zhiya_price) {
                    $price = $mijia_price;
                    $row['name'] = "【密价】{$row['name']}";
                } else {
                    $price = $zhiya_price;
                    $row['name'] = "【质押优惠】{$row['name']}";
                }
            } else if($mijia) {
                $price = $mijia_price;
                $row['name'] = "【密价】{$row['name']}";
            } else if($zhiya && $zhiya['discount_rate'] > 0) {
                $price = $zhiya_price;
                $row['name'] = "【质押优惠】{$row['name']}";
            }
            
            // 最后判断是否需要恢复原价
            if ($price >= $price1) {
                $price = $price1;
                // 如果恢复原价，移除价格标记
                $row['name'] = str_replace(['【密价】', '【质押优惠】'], '', $row['name']);
            }
            
            // 原有价格上限检查
            if ($price > $row['price'] * $userrow['addprice']) {
                $price = $row['price'] * $userrow['addprice'];
            }
            
            // 原有 suo 处理
            if ($row['suo'] != 0) {
                $price = $row['suo'];
            }
            
            $content = "#当前分类:" . $fenlei . " #商品ID:" . $row['cid'] . " <br> " . $row['content'];
            $fenleixin = $fenleixinxi['text'];
            $data[] = [
                'sort' => $row['sort'],
                'cid' => $row['cid'],
                'name' => $row['name'],
                'price' => $price,
                'content' => $content,
                'categoryIntro' => $fenleixin,
            ];
        }
        $stmt->close();
    }
    usort($data, function($a, $b) {
        if ($a['sort'] == $b['sort']) {
            return $b['cid'] <=> $a['cid'];
        }
        return $a['sort'] <=> $b['sort'];
    });
    $data = ['code' => 1, 'data' => $data];
    exit(json_encode($data));
break;

	case 'get':
	    $cid=trim(strip_tags(daddslashes($_POST['cid'])));
	    $userinfo=daddslashes($_POST['userinfo']);
	    $hash=daddslashes($_POST['hash']);
	    $rs=$DB->get_row("select * from qingka_wangke_class where cid='$cid' limit 1 ");   
	    $kms = str_replace(array("\r\n", "\r", "\n"), "[br]", $userinfo);
		$info=explode("[br]",$kms);
		
		$key='AES_Encryptwords';
		$iv='0123456789abcdef';
		$hash = openssl_decrypt($hash, 'aes-128-cbc', $key, 0 , $iv);
		if((empty($_SESSION['addsalt']) || $hash!=$_SESSION['addsalt'])){
			exit('{"code":-1,"msg":"验证失败，请刷新页面重试"}');
		}
				
		for($i=0;$i<count($info);$i++){
			 $str = merge_spaces(trim($info[$i]));
			 $userinfo2=explode(" ",$str);//分割

			 // 易教育项目特殊处理：使用noun字段而不是getnoun字段
			 $jxjy_huoyuan = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
			 $project_noun = ($jxjy_huoyuan && $rs['docking'] == $jxjy_huoyuan['hid']) ? $rs['noun'] : $rs['getnoun'];

			 if(count($userinfo2)>2){
			 	$result=getWk($rs['queryplat'],$project_noun,trim($userinfo2[0]),trim($userinfo2[1]),trim($userinfo2[2]),$rs['name']);
			 }else{
			 	$result=getWk($rs['queryplat'],$project_noun,"自动识别",trim($userinfo2[0]),trim($userinfo2[1]),$rs['name']);
			 }
		 	 $userinfo3=trim($userinfo2[0]." ".$userinfo2[1]." ".$userinfo2[2]);
		 	 $result['userinfo']=$userinfo3;
			 wlog($userrow['uid'],"查课","{$rs['name']}-查课信息：{$userinfo3}",0);
		}
	    exit(json_encode($result));      
    break;
    

case 'add':
    $cid = trim(strip_tags(daddslashes($_POST['cid'])));
    $data = daddslashes($_POST['data']);
    $clientip = real_ip();
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$cid' LIMIT 1");

    if ($cid == '' || $data == '') {
        exit('{"code":-1,"msg":"请选择课程"}');
    }

    // 计算初始单价
    if ($rs['yunsuan'] == "*") {
        $danjia = round($rs['price'] * $userrow['addprice'], 2);
    } elseif ($rs['yunsuan'] == "+") {
        $danjia = round($rs['price'] + $userrow['addprice'], 2);
    } else {
        $danjia = round($rs['price'] * $userrow['addprice'], 2);
    }
    $danjia1 = $danjia; // 保存初始单价用于后续比较

    // 处理密价
    $mijia = $DB->get_row("SELECT * FROM qingka_wangke_mijia WHERE uid='{$userrow['uid']}' AND cid='$cid'");
    $mijia_price = $danjia; // 默认等于当前单价
    if ($mijia) {
        if ($mijia['mode'] == 0) {
            $mijia_price = round($danjia - $mijia['price'], 2);
        } elseif ($mijia['mode'] == 1) {
            $mijia_price = round(($rs['price'] - $mijia['price']) * $userrow['addprice'], 2);
        } elseif ($mijia['mode'] == 2) {
            $mijia_price = $mijia['price'];
        }
        if ($mijia_price <= 0) {
            $mijia_price = 0;
        }
    }

    // 处理质押价格
    $zhiya_price = $danjia;
    $zhiya = $DB->get_row("SELECT zr.*, zc.discount_rate
        FROM qingka_wangke_zhiya_records zr
        LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id
        WHERE zr.uid='{$userrow['uid']}'
        AND zc.category_id='{$rs['fenlei']}'
        AND zr.status=1
        ORDER BY zr.id DESC LIMIT 1");

    if ($zhiya && $zhiya['discount_rate'] > 0) {
        $zhiya_price = round($danjia * $zhiya['discount_rate'], 2);
    }

    // 比较密价和质押价格，选择较低的价格
    if ($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
        if ($mijia_price <= $zhiya_price) {
            $danjia = $mijia_price;
        } else {
            $danjia = $zhiya_price;
        }
    } elseif ($mijia) {
        $danjia = $mijia_price;
    } elseif ($zhiya && $zhiya['discount_rate'] > 0) {
        $danjia = $zhiya_price;
    }

    // 如果最终价格不低于初始单价，恢复初始单价
    if ($danjia >= $danjia1) {
        $danjia = $danjia1;
    }

    // 检查单价和用户加成
    if ($danjia == 0 || $userrow['addprice'] < 0.1) {
        exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
    }

    // 计算总费用
    $money = count($data) * $danjia;
    if ($userrow['money'] < $money) {
        exit('{"code":-1,"msg":"余额不足"}');
    }

    foreach ($data as $row) {
        $userinfo = $row['userinfo'];
        $userName = $row['userName'];
        $userinfo = explode(" ", $userinfo); // 分割账号密码
        if (count($userinfo) > 2) {
            $school = $userinfo[0];
            $user = $userinfo[1];
            $pass = $userinfo[2];
        } else {
            $school = "自动识别";
            $user = $userinfo[0];
            $pass = $userinfo[1];
        }

        $kcid = $row['data']['id'];
        $kcname = $row['data']['name'];
        $kcjs = $row['data']['kcjs'];

        // 检查是否重复下单
        if ($DB->get_row("SELECT * FROM qingka_wangke_order WHERE ptname='{$rs['name']}' AND school='$school' AND user='$user' AND pass='$pass' AND kcid='$kcid' AND kcname='$kcname'")) {
            $dockstatus = '3'; // 重复下单
        } elseif ($rs['docking'] == 0) {
            $dockstatus = '99';
        } else {
            $dockstatus = '0';
        }

        // 插入订单
        $is = $DB->query("INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus)
            VALUES ('{$userrow['uid']}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$userName','$user','$pass','$kcid','$kcname','{$kcjs}','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus')");

        if ($is) {
            $DB->query("UPDATE qingka_wangke_user SET money = money - '{$danjia}' WHERE uid='{$userrow['uid']}' LIMIT 1");
            wlog($userrow['uid'], "添加任务", " {$rs['name']} {$user} {$pass} {$kcname} 扣除{$danjia}积分！", -$danjia);
        }
    }

    if ($is) {
        exit('{"code":1,"msg":"提交成功"}');
    } else {
        $error_msg = $DB->error();
        exit('{"code":-1,"msg":"提交失败: ' . addslashes($error_msg) . '"}');
    }
    break;



case 'add_pl':
    $cid = trim(strip_tags($_POST["cid"]));
    $data = $_POST["userinfo"];
    $num = $_POST["num"];

    $sql = "SELECT * FROM qingka_wangke_class WHERE cid=? LIMIT 1";
    $rs = $DB->prepare_getrow($sql, [$cid]);

    if($rs['yunsuan']=="*"){
        $danjia = round($rs['price'] * $userrow['addprice'], 2);
    }elseif($rs['yunsuan']=="+"){
        $danjia = round($rs['price'] + $userrow['addprice'], 2);
    }else{
        $danjia = round($rs['price'] * $userrow['addprice'], 2);
    }
    $danjia1 = $danjia;

    $sql = "SELECT * FROM qingka_wangke_mijia WHERE uid=? AND cid=?";
    $mijia = $DB->prepare_getrow($sql, [$userrow['uid'], $cid]);
    $mijia_price = $danjia;

    if($mijia){
        if ($mijia['mode']==0) {
            $mijia_price = round($danjia - $mijia['price'], 2);
        } elseif ($mijia['mode']==1) {
            $mijia_price = round(($rs['price'] - $mijia['price']) * $userrow['addprice'], 2);
        } elseif ($mijia['mode']==2) {
            $mijia_price = $mijia['price'];
        }
        if ($mijia_price <= 0) {
            $mijia_price = 0;
        }
    }

    $zhiya_price = $danjia;
    $sql = "SELECT zr.*, zc.discount_rate
        FROM qingka_wangke_zhiya_records zr
        LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id
        WHERE zr.uid=?
        AND zc.category_id=?
        AND zr.status=1
        ORDER BY zr.id DESC LIMIT 1";
    $zhiya = $DB->prepare_getrow($sql, [$userrow['uid'], $rs['fenlei']]);

    if ($zhiya && $zhiya['discount_rate'] > 0) {
        $zhiya_price = round($danjia * $zhiya['discount_rate'], 2);
    }

    if ($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
        if ($mijia_price <= $zhiya_price) {
            $danjia = $mijia_price;
        } else {
            $danjia = $zhiya_price;
        }
    } elseif ($mijia) {
        $danjia = $mijia_price;
    } elseif ($zhiya && $zhiya['discount_rate'] > 0) {
        $danjia = $zhiya_price;
    }

    if ($danjia >= $danjia1) {
        $danjia = $danjia1;
    }

    if($danjia==0 || $userrow['addprice']<0.1){
        exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
    }

    for ($i = 0; $i < $num; $i++) {
        $userinfo_a = trim($data[$i]);
        $userinfo_k = preg_replace("/\\s(?=\\s)/", "\\1", $userinfo_a);
        $userinfo = explode(" ", $userinfo_k);

        if (preg_match("/[\x7f-\xff]/", $userinfo[0])) {
        } else {
            if (!empty($userinfo[0])) {
                array_unshift($userinfo, "自动识别");
            }
        }

        if (preg_match("/[\x7f-\xff]/", $userinfo[2])) {
            exit("{\"code\":-1,\"msg\": \"格式错误，请修改后重新提交！！！\"}");
        }

        if (empty($userinfo[3]) || $userinfo[3] == NULL || $userinfo[3] == " ") {
            exit("{\"code\":-1,\"msg\": \"格式错误，请修改后重新提交！！！\"}");
        }

        for ($j = 3; $j < count($userinfo); $j++) {
            $new_info[] = [$userinfo[0], $userinfo[1], $userinfo[2], $userinfo[$j]];
        }
    }

    $money = count($new_info) * $danjia;

    if ($userrow["money"] < $money) {
        exit("{\"code\":-1,\"msg\": \"余额不足！\"}");
    }

    $date = date('Y-m-d H:i:s');
    $clientip = $_SERVER['REMOTE_ADDR'];
    $userName = htmlspecialchars($userrow['name']);
    $kcid = 0;
    $kcjs = 0;
    $miaoshua = 0;

    foreach ($new_info as $info) {
        $school = $info[0];
        $user = $info[1];
        $pass = $info[2];
        $kcname = $info[3];

        $sql = "SELECT * FROM qingka_wangke_order WHERE ptname=? AND school=? AND user=? AND pass=? AND kcid=? AND kcname=?";
        $params = [$rs["name"], $school, $user, $pass, $kcid, $kcname];
        if ($DB->prepare_getrow($sql, $params)) {
            $dockstatus = "3";
        } else {
            $dockstatus = ($rs["docking"] == 0) ? "99" : "0";
        }

        $sql = "INSERT INTO qingka_wangke_order (uid,cid,hid,ptname,school,name,user,pass,kcid,kcname,courseEndTime,fees,noun,miaoshua,addtime,ip,dockstatus) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        $params = [
            $userrow["uid"], $rs["cid"], $rs["docking"], $rs["name"], $school, $userName, $user, $pass, $kcid, $kcname, $kcjs, $danjia, $rs["noun"], $miaoshua, $date, $clientip, $dockstatus
        ];
        $is = $DB->prepare_query($sql, $params);

        if ($is) {
            $sql = "UPDATE qingka_wangke_user SET money=money-? WHERE uid=? LIMIT 1";
            $DB->prepare_query($sql, [$danjia, $userrow["uid"]]);

            wlog($userrow["uid"], "批量提交", " " . $rs["name"] . " " . $school . " " . $user . " " . $pass . " " . $kcname . " ", -1 * $danjia);
        }
    }

    exit("{\"code\":1,\"msg\":\"成功提交 " . count($new_info) . " 门课程,扣费" . $money . "元！！！\"}");
break;

		
case "loglist1":
    $page = intval($_POST["page"]);
    $pagesize = 25;
    $pageu = ($page - 1) * $pagesize;
    
    $params = [];
    $sql = " WHERE type='批量提交'";
    
    if ($userrow["uid"] != "1") {
        $sql .= " AND uid=?";
        $params[] = $userrow["uid"];
    }
    
    // 查询数据
    $sql_select = "SELECT * FROM qingka_wangke_log " . $sql . " ORDER BY id DESC LIMIT ?, ?";
    $params_select = array_merge($params, [$pageu, $pagesize]);
    $stmt = $DB->prepare_query($sql_select, $params_select);
    
    $data = [];
    if($stmt){
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
        $stmt->close();
    }
    
    // 查询总数
    $sql_count = "SELECT COUNT(*) FROM qingka_wangke_log " . $sql;
    $count1 = $DB->prepare_count($sql_count, $params);
    $last_page = ceil($count1 / $pagesize);
    
    $data = array("code" => 1, "data" => $data, "current_page" => (int) $page, "last_page" => $last_page);
    exit(json_encode($data));
    break;
    
case 'add_favorite':
    $cid = intval($_POST['cid']);
    $uid = $userrow['uid'];
    $redis->sAdd("user_favorites:{$uid}", $cid);
    exit(json_encode(array('code' => 1, 'msg' => '收藏成功')));
break;

case 'remove_favorite':
    $cid = intval($_POST['cid']);
    $uid = $userrow['uid'];
    $redis->sRem("user_favorites:{$uid}", $cid);
  exit(json_encode(array('code' => 1, 'msg' => '移除成功')));
break;

case 'shareclass':
    $cid = intval($_POST['cid']);
    $a = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$cid'");
    $b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE id='{$a['fenlei']}'");
    $price = $a['price'] * 0.2;
    $message = "向你分享了一个宝藏好物，位于分类：".$a['fenlei']."【".$b['name']."】产品名：".$a['name']."顶级价：【".$price."】产品介绍： 【".$a['content']."】最近卖爆了！还在犹豫什么快来下单吧！";
    exit(json_encode(array('code' => 1, 'msg' => $message)));
break;
//订单列表模块
case 'orderlist':
    $cx = daddslashes($_POST['cx']);
    $page = trim(strip_tags(daddslashes($_POST['page'])));
    $pagesize = trim(strip_tags($cx['pagesize']));
    $pageu = ($page - 1) * $pagesize;
    $school = trim(strip_tags($cx['school']));
    $qq = trim(strip_tags($cx['qq']));
    $status_text = trim(strip_tags($cx['status_text']));
    $dock = trim(strip_tags($cx['dock']));
    $cid = trim(strip_tags($cx['cid']));
    $oid = trim(strip_tags($cx['oid']));
    $uid = trim(strip_tags($cx['uid']));
    $hid = trim(strip_tags($cx['hid']));
    $remarks = trim(strip_tags($cx['remarks']));
    $kcname = trim(strip_tags($cx['kcname']));
    $mima = trim(strip_tags($cx['mima']));
    $start_time = trim(strip_tags($cx['dateRange'][0] ?? '')); // New start time
    $end_time = trim(strip_tags($cx['dateRange'][1] ?? ''));   // New end time
    // 构建 SQL 查询和参数数组
    $sql = "SELECT * FROM qingka_wangke_order WHERE 1=1";
    $count_sql = "SELECT COUNT(*) FROM qingka_wangke_order WHERE 1=1";
    $params = [];
    $count_params = [];

    if ($userrow['uid'] != '1') {
        $sql .= " AND uid = ?";
        $count_sql .= " AND uid = ?";
        $params[] = $userrow['uid'];
        $count_params[] = $userrow['uid'];
    }

    if ($kcname != '') {
        $sql .= " AND kcname LIKE ?";
        $count_sql .= " AND kcname LIKE ?";
        $params[] = "%{$kcname}%";
        $count_params[] = "%{$kcname}%";
    }

    if ($cid != '') {
        $sql .= " AND cid = ?";
        $count_sql .= " AND cid = ?";
        $params[] = $cid;
        $count_params[] = $cid;
    }

    if ($qq != '') {
        $sql .= " AND user = ?";
        $count_sql .= " AND user = ?";
        $params[] = $qq;
        $count_params[] = $qq;
    }

    if ($oid != '') {
        $sql .= " AND oid = ?";
        $count_sql .= " AND oid = ?";
        $params[] = $oid;
        $count_params[] = $oid;
    }

    if ($uid != '') {
        $sql .= " AND uid = ?";
        $count_sql .= " AND uid = ?";
        $params[] = $uid;
        $count_params[] = $uid;
    }

    if ($status_text != '') {
        $sql .= " AND status LIKE ?";
        $count_sql .= " AND status LIKE ?";
        $params[] = "%{$status_text}%";
        $count_params[] = "%{$status_text}%";
    }

    if ($dock != '') {
        $sql .= " AND dockstatus = ?";
        $count_sql .= " AND dockstatus = ?";
        $params[] = $dock;
        $count_params[] = $dock;
    }

    if ($hid != '') {
        $sql .= " AND hid = ?";
        $count_sql .= " AND hid = ?";
        $params[] = $hid;
        $count_params[] = $hid;
    }

    if ($remarks != '') {
        $sql .= " AND remarks LIKE ?";
        $count_sql .= " AND remarks LIKE ?";
        $params[] = "%{$remarks}%";
        $count_params[] = "%{$remarks}%";
    }

    if ($mima != '') {
        $sql .= " AND pass = ?";
        $count_sql .= " AND pass = ?";
        $params[] = $mima;
        $count_params[] = $mima;
    }

    if ($school != '') {
        $sql .= " AND school LIKE ?";
        $count_sql .= " AND school LIKE ?";
        $params[] = "%{$school}%";
        $count_params[] = "%{$school}%";
    }

    if ($start_time != '' && $end_time != '') {
        $sql .= " AND addtime > ? AND addtime < ?";
        $count_sql .= " AND addtime > ? AND addtime < ?";
        $params[] = $start_time;
        $params[] = $end_time;
        $count_params[] = $start_time;
        $count_params[] = $end_time;
    }

    // 添加排序和分页
    $sql .= " ORDER BY oid DESC LIMIT ?, ?";
    $params[] = (int)$pageu;
    $params[] = (int)$pagesize;

    // 执行查询
    $stmt = $DB->prepare_query($sql, $params);
    $data = [];
    if ($stmt) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            if ($row['name'] == '' || $row['name'] == 'undefined') {
                $row['name'] = 'null';
            }
            $data[] = $row;
        }
    }

    // 执行计数查询
    $count_stmt = $DB->prepare_query($count_sql, $count_params);
    $total_count = 0;
    if ($count_stmt) {
        $count_result = $count_stmt->get_result();
        $total_count = $count_result->fetch_row()[0];
    }

    $last_page = ceil($total_count / $pagesize); // 取最大页数

    $response = array(
        'code' => 1,
        'data' => $data,
        "current_page" => (int)$page,
        "last_page" => $last_page,
        "uid" => (int)$userrow['uid'],
        "total_count" => $total_count // 添加总条数
    );

    exit(json_encode($response));
    break;

       case 'bs':
       $oid=trim(strip_tags(daddslashes($_GET['oid'])));
       $b=$DB->get_row("select hid,cid,dockstatus,status from qingka_wangke_order where oid='{$oid}' "); 
	   if($b['status']=="已退款"){
            jsonReturn(-1,"都已经退款了，你还补个蛋的单！");
       }
       if($b['status']=="已取消"){
            jsonReturn(-1,"订单都已经取消了，你还补个蛋的单！");
       }else{
        	  $b=budanWk($oid);
        	  if($b['code']==1){
        	  	$DB->query("update qingka_wangke_order set status='补刷中',`dockstatus`=1,`bsnum`=bsnum+1 where oid='{$oid}' ");
        	  	jsonReturn(1,$b['msg']);
        	  }else{
        	    $DB->query("update qingka_wangke_order set status='补刷失败' where oid='{$oid}' ");
        	  	jsonReturn(-1,$b['msg']);
        	  }          
	    }  
    break;
    case 'zt':
		$oid = trim(strip_tags(daddslashes($_GET['oid'])));
		$b = $DB->get_row("select hid,cid,dockstatus from qingka_wangke_order where oid='{$oid}' ");
		if ($b['dockstatus'] == '99') {
			jsonReturn(1, "我的订单");
		} else {
			$b = ztWk($oid);
			if ($b['code'] == 1) {
				$DB->query("update qingka_wangke_order set status='已停止',`bsnum`=bsnum+1 where oid='{$oid}' ");
				wlog($userrow['uid'], "暂停", "暂停订单: {$oid}", 0);
				jsonReturn(1, $b['msg']);
			} else {
				jsonReturn(-1, $b['msg']);
			}
		}
		break;
    case 'uporder':
        $oid = trim($_GET['oid']);
        $sql = "SELECT * FROM qingka_wangke_order WHERE oid = ?";
        $row = $DB->prepare_getrow($sql, [$oid]);
        if ($row['dockstatus'] == '99') {
            jsonReturn(1, '实时进度无需更新');
        }elseif($row['dockstatus']!='1' and $row['dockstatus']!='4'){
           	    jsonReturn(-1,'订单尚未提交至上游');
           }
        $result = processCx($oid);
        foreach ($result as $item) {
            $sql = "UPDATE qingka_wangke_order SET `name` = ?, `yid` = ?, `status` = ?, `courseStartTime` = ?, `courseEndTime` = ?, `examStartTime` = ?, `examEndTime` = ?, `process` = ?, `remarks` = ? WHERE `user` = ? AND `kcname` = ? AND `oid` = ?";
            $params = [
                $item['name'],
                $item['yid'],
                $item['status_text'],
                $item['kcks'],
                $item['kcjs'],
                $item['ksks'],
                $item['ksjs'],
                $item['process'],
                $item['remarks'],
                $item['user'],
                $item['kcname'],
                $oid
            ];
            $DB->prepare_query($sql, $params);
        }
        exit('{"code":1,"msg":"同步成功"}');
        break;
        
        case 'plzt':
		$redis=new Redis();
        $redis->connect("127.0.0.1","6379");
        $sex=daddslashes($_POST['sex']); 
        $rediscode=$redis->ping();
        if ($rediscode==true) {
            for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $redis->lPush("plztoid",$oid);
                $DB->query("update qingka_wangke_order set status='待更新' where oid='{$oid}' ");
                
	       }       
	       wlog($userrow['uid'], "批量同步状态", "批量同步状态入队成功，共入队{$i}条",0);
	       jsonReturn(1,"批量同步状态入队成功，共入队{$i}条，请耐心等待同步");
        }else {
            jsonReturn(-1,"入队失败");
        }
    break;
    
       case 'plbs':
        $redis=new Redis();
        $redis->connect("127.0.0.1","6379");
        $sex=daddslashes($_POST['sex']); 
        $rediscode=$redis->ping();
        if ($rediscode==true) {
            for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $redis->lPush("plbsoid",$oid);
                $DB->query("update qingka_wangke_order set status='待更新' where oid='{$oid}' ");
	       }       
	       wlog($userrow['uid'], "批量补刷", "批量补刷入队成功，共入队{$i}条",0);
	       jsonReturn(1,"批量补刷入队成功，共入队{$i}条，请耐心等待补刷成功");
        }else {
            jsonReturn(-1,"入队失败");
        }
    break;
    
        case 'plmiaoshua':
        $redis=new Redis();
        $redis->connect("127.0.0.1","6379");
        $sex=daddslashes($_POST['sex']); 
        $rediscode=$redis->ping();
        if ($rediscode==true) {
            for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $redis->lPush("plmiaoshuaoid",$oid);
                $DB->query("update qingka_wangke_order set status='待更新' where oid='{$oid}' ");
	       }       
	       wlog($userrow['uid'], "批量转秒", "批量转秒成功，共入队{$i}条",0);
	       jsonReturn(1,"批量转秒入队成功，共入队{$i}条，请耐心等待转秒完成");
        }else {
            jsonReturn(-1,"入队失败");
        }
    break;
    
        case 'pltx':
        $redis=new Redis();
        $redis->connect("127.0.0.1","6379");
        $sex=daddslashes($_POST['sex']); 
        $rediscode=$redis->ping();
        if ($rediscode==true) {
            for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $redis->lPush("pltxoid",$oid);
                $DB->query("update qingka_wangke_order set status='待更新' where oid='{$oid}' ");
	       }       
	       wlog($userrow['uid'], "批量停止", "批量补刷停止成功，共入队{$i}条",0);
	       jsonReturn(1,"批量停止入队成功，共入队{$i}条，请耐心等待停止完成");
        }else {
            jsonReturn(-1,"入队失败");
        }
    break;
    
    	case 'duijie':
	    $oid = trim($_GET['oid']);
	    $b = $DB->prepare_getrow("SELECT * FROM qingka_wangke_order WHERE oid=? LIMIT 1", [$oid]);
		  if($userrow['uid']!=1){
		  	exit('{"code":-2,"msg":"无权限"}');
		  }
		$d = $DB->prepare_getrow("SELECT * FROM qingka_wangke_class WHERE cid=?", [$b['cid']]);
	 	$result=addWk($oid);
	  	if($result['code']=='1'){
       	    $DB->prepare_query("UPDATE qingka_wangke_order SET `hid`=?,`status`='进行中',`dockstatus`=1,`yid`=? WHERE oid=?", [$d['docking'], $result['yid'], $oid]);
        }else{
        	$DB->prepare_query("UPDATE qingka_wangke_order SET `dockstatus`=2 WHERE oid=?", [$oid]);
        }
	    exit(json_encode($result,true));
	break;

    
    case 'ms':
		$oid = trim(strip_tags(daddslashes($_GET['oid'])));
		$b = $DB->get_row("select cid,dockstatus from qingka_wangke_order where oid='{$oid}' ");
       if ($b['dockstatus'] == '99') {
			jsonReturn(1, "我的订单");
		} else {
			$b = msWk($oid);
			if ($b['code'] == 1) {
              $DB->query("update qingka_wangke_user set money=money-0.01 where uid='{$userrow['uid']}' limit 1 ");
				wlog($userrow['uid'], "秒刷", "订单{$oid}成功提交扣除0.01", -0.01);
				jsonReturn(1, $b['msg']);
			} else {
				jsonReturn(-1, $b['msg']);
			}
		}
    break;


    case 'xgmm':
    $xgmm = trim(strip_tags($_GET['xgmm']));
    $oid = $_GET['oid'];
    if (empty($xgmm)) {
        jsonReturn(-1, "密码不能为空");
    }
    if (strlen($xgmm) < 3) {
        jsonReturn(-1, "密码长度至少为3位");
    } else {
        $b = xgmm($oid, $xgmm);
        if ($b['code'] == 1) {
            $sql = "UPDATE qingka_wangke_order SET pass = ? WHERE oid = ?";
            $params = [$xgmm, $oid];
            $DB->prepare_query($sql, $params);

            $sql = "update qingka_wangke_user set money=money-0.05 where uid=? limit 1 ";
            $params = [$userrow['uid']];
            $DB->prepare_query($sql, $params);

            wlog($userrow['uid'], "修改密码", "订单{$oid}修改密码成功扣除0.05", -0.01);
            jsonReturn(1, $b['msg']);
        } else {
            jsonReturn(-1, $b['msg']);
        }
    }
    break;
    
    case 'qx_order':
        $oid = trim($_GET['oid']);
        $sql = "SELECT * FROM qingka_wangke_order WHERE oid = ?";
        $row = $DB->prepare_getrow($sql, [$oid]);
        if ($row['uid'] != $userrow['uid'] && $userrow['uid'] != 1) {
            jsonReturn(-1, "无权限");
        } else {
            $sql = "UPDATE qingka_wangke_order SET `status` = '已取消', `dockstatus` = 4 WHERE oid = ?";
            $DB->prepare_query($sql, [$oid]);
            jsonReturn(1, "取消成功");
        }
        break;
        
case 'status_order':
    $a = trim($_GET['a']);
    $sex = $_POST['sex'];
    $type = trim($_POST['type']);

    if ($userrow['uid'] == 0) {
        jsonReturn(-1, "老铁，您还没有登录哦！");
    }

    if ($userrow['uid'] == 1) {
        if ($type == 1) {
            $sql = "`status`=?";
        } elseif ($type == 2) {
            $sql = "`dockstatus`=?";
        } else {
            jsonReturn(-1, "无效的操作类型");
        }
    } else {
        if ($type != 1) {
            jsonReturn(-1, "您无权限修改此字段");
        }
        $sql = "`status`=?";
    }
    try {
        foreach ($sex as $oid) {
            $oid = intval($oid);
            if ($oid <= 0) {
                continue;
            }

            $whereClause = ($userrow['uid'] == 1)
                ? "oid=?"
                : "uid=? AND oid=?";

            $updateSql = "UPDATE qingka_wangke_order SET {$sql} WHERE {$whereClause}";

            if ($userrow['uid'] == 1) {
                $params = [$a, $oid];
            } else {
                $params = [$a, $userrow['uid'], $oid];
            }
            $stmt = $DB->prepare_query($updateSql, $params);
            if (!$stmt) {
                throw new Exception("更新失败");
            }
            $stmt->close();
        }
        jsonReturn(1, "修改成功");
    } catch (Exception $e) {
        jsonReturn(-1, "未知异常：" . $e->getMessage());
    }
    break;

	case 'tk':
        $sex=daddslashes($_POST['sex']); 
        if($userrow['uid']==1){
	        for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $order = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
                $user = $DB->get_row("select * from qingka_wangke_user where uid='{$order['uid']}' ");
                $DB->query("update qingka_wangke_user set money=money+'{$order['fees']}' where uid='{$user['uid']}'");
                $DB->query("update qingka_wangke_order set status='已退款',dockstatus='4' where oid='{$oid}'");
                wlog($user['uid'], "订单退款", "订单ID：{$order['oid']} 订单信息：{$order['user']}{$order['kcname']}被管理员退款", "+{$order['fees']}");
	       }        
       	   exit('{"code":1,"msg":"选择的订单已批量退款！可在日志中查看！"}');     
       }else{
        	exit('{"code":-1,"msg":"无权限"}');
       }
    break; 
        case 'sc':
        $sex=daddslashes($_POST['sex']); 
        if($userrow['uid']==1){
	        for($i=0;$i<count($sex);$i++){
	            $oid=$sex[$i];
                $order = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
                $user = $DB->get_row("select * from qingka_wangke_user where uid='{$order['uid']}' ");
                $DB->query("delete from qingka_wangke_order where oid='{$oid}'");
                wlog($user['uid'], "删除订单信息", "订单ID：{$order['oid']} 订单信息：{$order['user']}{$order['kcname']}被管理员删除", "+0");
	       }        
            exit('{"code":1,"msg":"选择的订单已批量删除！"}');     
       }else{
            exit('{"code":-1,"msg":"别乱搞，单子丢了钱你赔吗？"}');
       }
    break;
//账号统计
case 'userquery':
    $cx = $_POST['cx'];
    $page = max(1, (int)$_POST['page']);
    $pagesize = max(1, (int)($cx['pagesize'] ?? 10));
    $pageu = ($page - 1) * $pagesize;
    $cid = trim($cx['cid'] ?? '');
    $school = trim($cx['school'] ?? '');
    $kcname = trim($cx['kcname'] ?? '');
    if ($userrow['uid'] != '1') {
        $base_sql = "WHERE uid = ?";
        $base_params = [$userrow['uid']];
    } else {
        $base_sql = "WHERE 1=1";
        $base_params = [];
    }
    $conditions = [];
    if (!empty($cid)) {
        $conditions[] = "cid = ?";
        $base_params[] = (int)$cid;
    }
    if (!empty($school)) {
        $conditions[] = "school LIKE ?";
        $base_params[] = "%" . $school . "%";
    }
    if (!empty($kcname)) {
        $conditions[] = "kcname LIKE ?";
        $base_params[] = "%" . $kcname . "%";
    }
    if (!empty($conditions)) {
        $base_sql .= " AND " . implode(" AND ", $conditions);
    }
    $query_sql = "SELECT 
                user,
                MAX(school) AS school,
                MAX(pass) AS pass,
                COUNT(user) AS count
              FROM qingka_wangke_order
              {$base_sql}
              GROUP BY user
              ORDER BY MAX(oid) DESC
              LIMIT {$pageu}, {$pagesize}";
    $stmt = $DB->prepare_query($query_sql, $base_params);
    $result = $stmt->get_result();
    $count_sql = "SELECT COUNT(DISTINCT user) AS total FROM qingka_wangke_order {$base_sql}";
    $total_count = $DB->prepare_count($count_sql, $base_params);
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'school' => $row['school'] ?: 'null',
            'user' => $row['user'] ?: 'null',
            'pass' => $row['pass'] ?: 'null',
            'count' => (int)$row['count']
        ];
    }
    $stmt->close();
    exit(json_encode([
        'code' => 1,
        'data' => $data,
        'current_page' => (int)$page,
        'last_page' => ceil($total_count / $pagesize),
        'total_count' => $total_count
    ]));
break;
    
//账号核对
	case 'orderusertest':
    $cid = trim($_POST['cid']);
    $userinfo = $_POST['userinfo'];
    $result = array();

    $sql_condition = ($userrow['uid'] != '1') ? "uid = ?" : "1=1";
    $params = ($userrow['uid'] != '1') ? [$userrow['uid']] : [];

    $sql = "SELECT oid FROM qingka_wangke_order WHERE " . $sql_condition;

    if($cid != '') {
        $sql .= " AND cid = ?";
        $params[] = $cid;
    }

    $userinfo2 = explode(" ", $userinfo);
    $user = (count($userinfo2) > 2) ? trim($userinfo2[1]) : trim($userinfo2[0]);
    $sql .= " AND user = ?";
    $params[] = $user;

    $row = $DB->prepare_getrow($sql, $params);

    if($row) {
        $result['code'] = 1;
        $result['data'] = '存在';
    } else {
        $result['code'] = -1;
        $result['data'] = '不存在';
    }

    exit(json_encode($result));
break;
//kcid比对
	case 'kcidlist':
	    $page=trim(daddslashes($_GET['page']));
	    $limit=trim(daddslashes($_GET['limit']));
	    $pageu = ($page - 1) * $limit;//当前界面	
	    if($userrow['uid']==1){
	        $a=$DB->query("select * from qingka_wangke_order order by oid desc limit $pageu,$limit");
	        $count=$DB->count("select count(*) from qingka_wangke_order");
	    }else{
	        $a=$DB->query("select * from qingka_wangke_order  where uid='{$userrow['uid']}' order by oid desc limit $pageu,$limit");
	        $count=$DB->count("select count(*) from qingka_wangke_order  where uid='{$userrow['uid']}'");
	    }
	    while($row=$DB->fetch($a)){
	   	   $data[]=array(
	   	        'oid'=>$row['oid'],
   	            'ptname'=>$row['ptname'],
	   	        'user'=>$row['user'],
	   	        'kcname'=>$row['kcname'],
	   	        'kcid'=>$row['kcid'],
	   	        'addtime'=>$row['addtime'],
	   	        'status'=>$row['status'],
	   	   );
	    }
	    
	    //array_multisort($sort, SORT_ASC, $rate, SORT_ASC, $data);
	    $data=array('code'=>1,'data'=>$data,"count"=>$count);
	    exit(json_encode($data));
	break;
	
//我的上级
case 'uuidinfo':
    if($userrow['uuid'] == 1) {
        $uuid=$DB->get_row("select addprice,endtime from qingka_wangke_user where uid='{$userrow['uuid']}'");
        $data=array(
            'code'=>1,
            'msg'=>'查询成功',
            'money'=>'站长信息无权查看！',
            'zcz'=>'站长信息无权查看！',
            'addprice'=>$uuid['addprice'],
            'yqm'=>'站长信息无权查看！',
            'addtime'=>'站长信息无权查看！',
            'endtime'=>$uuid['endtime'],
            'dailishu'=>'站长信息无权查看！',                         
        );
    } else {
        $uuid=$DB->get_row("select * from qingka_wangke_user where uid='{$userrow['uuid']}'");
        $dltj=$DB->count("select count(uid) from qingka_wangke_user where uuid='{$userrow['uuid']}'");
        $data=array(
            'code'=>1,
            'msg'=>'查询成功',
            'money'=>round($uuid['money'],2),
            'zcz'=>$uuid['zcz'],
            'addprice'=>$uuid['addprice'],
            'yqm'=>$uuid['yqm'],
            'addtime'=>$uuid['addtime'],
            'endtime'=>$uuid['endtime'],
            'dailishu'=>$dltj                  
        );
    }
    exit(json_encode($data));
break;


//代理管理
  case 'adduser':
	    if($conf['user_htkh']=='0'){
	    	jsonReturn(-1,"暂停开户，具体开放时间等通知");
	    }
        parse_str(daddslashes($_POST['data']),$row);
        $type=daddslashes($_POST['type']);
        $row['user'] = trim($row['user']);
        $row['pass'] = trim($row['pass']);
        if(empty($row['name']) || empty($row['user']) || empty($row['pass']) || empty($row['addprice'])){
        	exit('{"code":-2,"msg":"所有项目不能为空"}');
        }
        if(!preg_match('/[1-9]([0-9]{4,10})/', $row['user']))exit('{"code":-1,"msg":"账号必须为QQ号"}');

        // 使用预处理查询判断账号是否存在
        $sql = "SELECT * FROM qingka_wangke_user WHERE user = ?";
        $params = [$row['user']];
        $existingUser = $DB->prepare_getrow($sql, $params);
        if ($existingUser) {
            exit('{"code":-1,"msg":"该账号已存在"}');
        }

        // 使用预处理查询判断昵称是否存在
        $sql = "SELECT * FROM qingka_wangke_user WHERE name = ?";
        $params = [$row['name']];
        $existingName = $DB->prepare_getrow($sql, $params);
        if ($existingName) {
            exit('{"code":-1,"msg":"该昵称已存在"}');
        }
        

		if($row['addprice']<$userrow['addprice']){
			exit('{"code":-1,"msg":"费率不能比自己低哦"}');
		}

		if($row['addprice']*100 % 5 !=0){
    		jsonReturn(-1,"请输入单价为0.05的倍数");
	    }
	    if($row['addprice']<0.2 || $row['addprice']>0.8){
    		jsonReturn(-1,"费率不合法！");
	    }
			$cz=0;
			$h=$DB->query("select * from qingka_wangke_dengji");
			while($row1=$DB->fetch($h)){
			    if($row['addprice']==$row1['rate']){
			        if ($row1['addkf']==1) {
			        $cz=$row1['money'];
			        }
			    }

			}
            $kochu=round($cz*($userrow['addprice']/$row['addprice']),2);
		    $kochu2=$kochu+$conf['user_ktmoney'];
		    if($type!=1){
        	   jsonReturn(1,"开通扣{$conf['user_ktmoney']}积分开户费，并自动给下级充值{$cz}积分，将扣除{$kochu}余额");
            }
			if($userrow['money']>=$kochu2){
	           // 使用预处理插入新用户
                $sql = "INSERT INTO qingka_wangke_user (uuid, user, pass, name, addprice, addtime) VALUES (?, ?, ?, ?, ?, ?)";
                $params = [$userrow['uid'], $row['user'], $row['pass'], $row['name'], $row['addprice'], $date];
                $DB->prepare_query($sql, $params);

                // 使用预处理更新用户余额
                $sql = "UPDATE qingka_wangke_user SET money = money - ? WHERE uid = ?";
                $params = [$conf['user_ktmoney'], $userrow['uid']];
                $DB->prepare_query($sql, $params);
	           wlog($userrow['uid'],"添加商户","添加商户{$row['user']}成功!扣费{$conf['user_ktmoney']}积分!","-{$conf['user_ktmoney']}");

	           if($cz!=0){
	           	 // 使用预处理更新下级用户金额
                 $sql = "UPDATE qingka_wangke_user SET money = ?, zcz = zcz + ? WHERE user = ?";
                 $params = [$cz, $cz, $row['user']];
                 $DB->prepare_query($sql, $params);
                  // 使用预处理更新上级用户金额
                 $sql = "UPDATE qingka_wangke_user SET money = money - ? WHERE uid = ?";
                 $params = [$kochu, $userrow['uid']];
                 $DB->prepare_query($sql, $params);
	           	 wlog($userrow['uid'],"代理充值","成功给账号为[{$row['user']}]的靓仔充值{$cz}积分,扣除{$kochu}积分",-$kochu);
	             $is=$DB->get_row("select uid from qingka_wangke_user where user='{$row['user']}' limit 1");
	             wlog($is['uid'],"上级充值","你上面的靓仔[{$userrow['name']}]成功给你充值{$cz}积分",+$cz);
	           }
	           exit('{"code":1,"msg":"添加成功"}');
		   }else{
		    	jsonReturn(-1,"余额不足开户，开户需扣除开户费{$conf['user_ktmoney']}积分，及余额{$kochu}积分");

		    }
    break;

case 'khcz':
    if ($userrow['khqx']!= 1 && $userrow['uid']!= 1) {
        exit('{"code":-1,"msg":"您没有跨户权限"}');
    }
    if ($userrow['uuid'] != 1) {
        exit('{"code":-1,"msg":"你想干嘛"}');
    }
    $uid = trim(strip_tags(daddslashes($_POST['uid'])));
    $money = trim(strip_tags(daddslashes($_POST['money'])));
    if (!preg_match('/^(-)?[0-9.]+$/', $money)) exit('{"code":-1,"msg":"充值金额不合法"}');
    if ($money < 20 && $userrow['uid'] != 1) {
        exit('{"code":-1,"msg":"跨户充值20起"}');
    }
    // 使用预处理查询目标用户
    $sql = "SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1";
    $params = [$uid];
    $row = $DB->prepare_getrow($sql, $params);
    if ($userrow['uid'] == $uid) {
        exit('{"code":-1,"msg":"自己不能给自己充值哦"}');
    }
    $kochu = round($money * ($userrow['addprice'] / $row['addprice']), 2);

    if ($userrow['money'] < $kochu) {
        exit('{"code":-1,"msg":"您当前沙子不足,无法充值"}');
    }
    if ($kochu == 0) {
        exit('{"code":-1,"msg":"你在干你妈臭逼呢？"}');
    }
    $wdkf = round($userrow['money'] - $kochu, 2);
    $xjkf = round($row['money'] + $money, 2);

    // 使用预处理更新用户余额
    $sql1 = "UPDATE qingka_wangke_user SET money = ? WHERE uid = ?";
    $params1 = [$wdkf, $userrow['uid']];
    $DB->prepare_query($sql1, $params1);


    $sql2 = "UPDATE qingka_wangke_user SET money = ?, zcz = zcz + ? WHERE uid = ?";
    $params2 = [$xjkf, $money, $uid];
    $DB->prepare_query($sql2, $params2);


    wlog($userrow['uid'], "代理充值", "成功给账号为[{$row['user']}]的靓仔充值{$money}沙子,扣除{$kochu}沙子", -$kochu);
    wlog($row['uid'], "跨户充值", "{$userrow['name']}成功给你充值{$money}沙子", +$money);
    exit('{"code":1,"msg":"充值' . $money . '沙子成功,实际扣费' . $kochu . '沙子"}');
    break;

	case 'userlist':
		$type = trim(strip_tags(daddslashes($_POST['type'])));
		$qq = trim(strip_tags(daddslashes($_POST['qq'])));
		$page = trim(daddslashes($_POST['page']));
		$pagesize = 10;
		$pageu = ($page - 1) * $pagesize; // 当前界面

		$sql = "WHERE 1=1";
		$params = [];

		if ($userrow['uid'] != '1') {
			$sql .= " AND uuid=?";
			$params[] = $userrow['uid'];
		}
		if ($qq != "") {
			switch ($type) {
				case '1':
					$sql .= " AND uid=?";
					$params[] = $qq;
					break;
				case '2':
					$sql .= " AND user=?";
					$params[] = $qq;
					break;
				case '3':
					$sql .= " AND yqm=?";
					$params[] = $qq;
					break;
				case '4':
					$sql .= " AND name=?";
					$params[] = $qq;
					break;
				case '5':
					$sql .= " AND addprice=?";
					$params[] = $qq;
					break;
				case '6':
					$sql .= " AND money=?";
					$params[] = $qq;
					break;
				case '7':
					$sql .= " AND endtime>?";
					$params[] = $qq;
					break;
				case '8':
					$sql .= " AND uuid=?";
					$params[] = $qq;
					break;
			}
		}

		$sql_select = "SELECT * FROM qingka_wangke_user {$sql} ORDER BY uid DESC LIMIT {$pageu}, {$pagesize}";
		$a = $DB->prepare_query($sql_select, $params);
		$result = $a->get_result();

		$sql_count = "SELECT COUNT(*) FROM qingka_wangke_user {$sql}";
		$count_stmt = $DB->prepare_query($sql_count, $params);
		$count_result = $count_stmt->get_result();
		$count_row = $count_result->fetch_row();
		$count1 = $count_row[0];
		$count_stmt->close();

		$data = [];
		while ($row = $result->fetch_assoc()) {
			$mijia_sql = "SELECT * FROM qingka_wangke_mijia WHERE uid=?";
			$mijia_params = [$row['uid']];
			$b = $DB->prepare_getrow($mijia_sql, $mijia_params);

			if ($b) {
				$row['user'] = $row['user'] . '【秘】';
			}
			$zcz = 0;
			$row['pass'] = "这还能让你知道？";
			if ($row['key'] != '0') {
				$row['key'] = '1';
			}

			$order_sql = "SELECT COUNT(oid) FROM qingka_wangke_order WHERE uid=?";
			$order_params = [$row['uid']];
			$dd = $DB->prepare_count($order_sql, $order_params);
			$row['dd'] = $dd;
			$data[] = $row;
		}
		$a->close();

		$last_page = ceil($count1 / $pagesize); // 取最大页数
		$data = array('code' => 1, 'data' => $data, "current_page" => (int)$page, "last_page" => $last_page);
		exit(json_encode($data));
		break;
		
    	case 'szyqm':
	   $uid = trim($_POST['uid']);
	   $yqm = trim($_POST['yqm']);

	   if(strlen($yqm) < 4){
	   	jsonReturn(-1, "邀请码最少4位，且必须为数字");
	   }
	   if(!is_numeric($yqm)){
	    	jsonReturn(-1, "请正确输入邀请码，必须为数字");
	   }
	   if($DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE yqm = ?", [$yqm])){
	    	jsonReturn(-1, "该邀请码已被使用，请换一个");
	   }

	   $a = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE uid = ?", [$uid]);
	   if($userrow['uid'] == '1'){
	   	  $DB->prepare_query("UPDATE qingka_wangke_user SET yqm = ? WHERE uid = ?", [$yqm, $uid]);
	   	  wlog($userrow['uid'], "设置邀请码", "给下级设置邀请码{$yqm}成功", '0');
	   	  jsonReturn(1, "设置成功");
	   }elseif($userrow['uid'] == $a['uuid']){
	   	  $DB->prepare_query("UPDATE qingka_wangke_user SET yqm = ? WHERE uid = ?", [$yqm, $uid]);
	   	  wlog($userrow['uid'], "设置邀请码", "给下级设置邀请码{$yqm}成功", '0');
	   	  jsonReturn(1, "设置成功");
	   }else{
	   	  jsonReturn(-1, "无权限");
	   }

	break;
    
	case 'adddjlist':
	    $a=$DB->query("select * from qingka_wangke_dengji where status=1 and rate>='{$userrow['addprice']}' order by sort desc");
	    while($row=$DB->fetch($a)){
	   	   $data[]=array(
	   	        'sort'=>$row['sort'],
   	            'name'=>$row['name'],
	   	        'rate'=>$row['rate'],
	   	   );
	    }
	    foreach ($data as $key => $row)
            {
                $sort[$key]  = $row['sort'];
                $name[$key] = $row['name'];
                $rate[$key] = $row['rate'];
            }
	    array_multisort($sort, SORT_ASC, $rate, SORT_ASC, $data);
	    $data=array('code'=>1,'data'=>$data);
	    exit(json_encode($data));
	break;
	
	case 'userjk':
	    $uid=trim(strip_tags(daddslashes($_POST['uid'])));
	    $money=trim(strip_tags(daddslashes($_POST['money'])));
	    if(!preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"充值金额不合法"}');
	    //充值扣费计算：扣除费用=充值金额*(我的总费率/代理费率-等级差*2%)
	    if($money<1 && $userrow['uid']!=1){
	    	exit('{"code":-1,"msg":"最低充值1积分"}');
	    }
        $row=$DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
	    if($row['uuid']!=$userrow['uid'] && $userrow['uid']!=1){
	    	exit('{"code":-1,"msg":"该用户你的不是你的下级,无法充值"}');
	    }
	    if($userrow['uid']==$uid){
	    	exit('{"code":-1,"msg":"自己不能给自己充值哦"}');
	    }
	    
	    $kochu=round($money*($userrow['addprice']/$row['addprice']),2);//充值	
	    	    
	    if($userrow['money']<$kochu){
	    	exit('{"code":-1,"msg":"您当前余额不足,无法充值"}');
	    }
	    if($kochu==0){
	    	exit('{"code":-1,"msg":"你在干你妈臭逼呢？"}');
	    }
	    $wdkf=round($userrow['money']-$kochu,2);
	    $xjkf=round($row['money']+$money,2);    
	    $DB->query("update qingka_wangke_user set money='$wdkf' where uid='{$userrow['uid']}' ");//我的扣费
	    $DB->query("update qingka_wangke_user set money='$xjkf',zcz=zcz+'$money' where uid='$uid' ");//下级增加	    
	    wlog($userrow['uid'],"代理充值","成功给账号为[{$row['user']}]的靓仔充值{$money}积分,扣除{$kochu}积分",-$kochu);
	    wlog($row['uid'],"上级充值","{$userrow['name']}成功给你充值{$money}积分",+$money);
	    exit('{"code":1,"msg":"充值'.$money.'积分成功,实际扣费'.$kochu.'积分"}');
   
	break;
	case 'userkc1':
	    $uid=trim(strip_tags(daddslashes($_POST['uid'])));
	    $money=trim(strip_tags(daddslashes($_POST['money'])));
	    if(!preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"金额不合法"}');
	    //充值扣费计算：扣除费用=充值金额*(我的总费率/代理费率-等级差*2%)
	    
        $row=$DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
	    if($row['uuid']!=$userrow['uid'] && $userrow['uid']!=1){
	    	exit('{"code":-1,"msg":"该用户你的不是你的下级,无法扣除余额"}');
	    }
	    if($userrow['uid']==$uid){
	    	exit('{"code":-1,"msg":"自己不能给自己扣款哦"}');
	    }
	    
	    $kochu=round($money*($userrow['addprice']/$row['addprice']),2);//充值	
	    	    
	    if($userrow['money']<$kochu){
	    	exit('{"code":-1,"msg":"您当前余额不足,无法充值"}');
	    }
	    if($kochu==0){
	    	exit('{"code":-1,"msg":"你在干你妈臭逼呢？"}');
	    }
	    $wdkf=round($userrow['money']-$kochu,2);
	    $xjkf=round($row['money']+$money,2);    
	    $DB->query("update qingka_wangke_user set money='$wdkf' where uid='{$userrow['uid']}' ");//我的扣费
	    $DB->query("update qingka_wangke_user set money='$xjkf',zcz=zcz+'$money' where uid='$uid' ");//下级增加	    
	    wlog($userrow['uid'],"代理充值","成功给账号为[{$row['user']}]的靓仔充值{$money}积分,扣除{$kochu}积分",-$kochu);
	    wlog($row['uid'],"上级充值","{$userrow['name']}成功给你充值{$money}积分",+$money);
	    exit('{"code":1,"msg":"充值'.$money.'积分成功,实际扣费'.$kochu.'积分"}');
   
	break;
	case 'usergj':
	    parse_str(daddslashes($_POST['data']),$row);
	    $uid=trim(strip_tags(daddslashes(trim($row['uid']))));
	    $addprice=trim(strip_tags(daddslashes($row['addprice'])));
	    $type=trim(strip_tags(daddslashes($_POST['type'])));
	    if(!preg_match('/^[0-9.]+$/', $addprice))exit('{"code":-1,"msg":"费率不合法"}');
	    
        $row=$DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
	    if($row['uuid']!=$userrow['uid'] && $userrow['uid']!=1){
	    	exit('{"code":-1,"msg":"该用户你的不是你的下级,无法修改价格"}');
	    }
	    if($userrow['uid']==$uid){
	    	exit('{"code":-1,"msg":"自己不能给自己改价哦"}');
	    }
	    if($userrow['addprice']>$addprice){
	    	exit('{"code":-1,"msg":"你下级的费率不能低于你哦"}');
	    }


    	if($addprice*100 % 5 !=0){
    		jsonReturn(-1,"请输入单价为0.05的倍数");
	    }
  
		if($addprice==$row['addprice']){
			jsonReturn(-1,"该商户已经是{$addprice}费率了，你还修改啥");
		}				
		if($addprice>$row['addprice'] && $userrow['uid']!=1){
			jsonReturn(-1,"下调费率，请联系管理员");
		}
		if($addprice<'0.2' && $userrow['uid']!=1){
			exit('{"code":-1,"msg":"你在干什么？"}');
		}
		
		//降价扣费计算：下级余额 /当前费率 *修改费率 ；
		$money=round($row['money']/$row['addprice']*$addprice,2);//涨降价余额变动,,自动调费
        $money1=$money-$row['money'];//日志显示变动余额
		$cz=0;
		$h=$DB->query("select * from qingka_wangke_dengji");
		while($row1=$DB->fetch($h)){
		    if($addprice==$row1['rate']){
		        if ($row1['gjkf']==1) {
			        $cz=$row1['money'];
		        }
		    }
		}
		$kochu=round($cz*($userrow['addprice']/$addprice),2);//充值	
		$kochu2=$kochu+$money+3;
        if($type!=1){
        	jsonReturn(1,"改价手续费3积分，并自动给下级[UID:{$uid}]充值{$cz}积分，将扣除{$kochu}余额");
        }

		if($userrow['money']<$kochu2){
           jsonReturn(-1,"余额不足,改价需扣3积分手续费,及余额{$kochu}积分");	
	    }else{
	       $DB->query("update qingka_wangke_user set money=money-3 where uid='{$userrow['uid']}' ");	          
           $DB->query("update qingka_wangke_user set money='$money',addprice='$addprice' where uid='$uid' ");//调费       
		   wlog($userrow['uid'],"修改费率","修改代理{$row['name']},费率：{$addprice},扣除手续费3积分","-3");
           wlog($uid,"修改费率","{$userrow['name']}修改你的费率为：{$addprice},系统根据比例自动调整价格",$money1);
          if($cz!=0){
          	$DB->query("update qingka_wangke_user set money=money-'{$kochu}' where uid='{$userrow['uid']}' ");//我的扣费
		    $DB->query("update qingka_wangke_user set money=money+'{$cz}',zcz=zcz+'$cz' where uid='$uid' ");//下级增加	    
		    wlog($userrow['uid'],"代理充值","成功给账号为[{$row['user']}]的靓仔充值{$cz}积分,扣除{$kochu}积分",-$kochu);
		    wlog($uid,"上级充值","{$userrow['name']}成功给你充值{$cz}积分",+$cz);
          }
          exit('{"code":1,"msg":"改价成功"}');	    	
	    }  
	break;
	
	case 'user_czmm':
	    $uid=trim(strip_tags(daddslashes($_POST['uid'])));
	    if($userrow['uid']==$uid){
	    	jsonReturn(-1,"自己不能给自己重置哦");
	    }
	    $row=$DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
	    if($row['uuid']!=$userrow['uid'] && $userrow['uid']!=1){
	    	exit('{"code":-1,"msg":"该用户你的不是你的下级,无法重置"}');
	    }else{
	    	$DB->query("update qingka_wangke_user set pass='1234567' where uid='{$uid}' ");
	    	wlog($row['uid'],"重置密码","成功重置UID为{$uid}的密码为1234567",0);
	    	jsonReturn(1,"成功重置密码为1234567");
	    }  
	break;
	
	case 'user_ban':
	    $uid = $_POST['uid'];
	    $active = $_POST['active'];
	    if($userrow['uid'] != 1){
	       jsonReturn(-1,"无权限");
	    }
        if($active == 1){
        	$a = 0;
        	$b = "封禁商户";
        }else{
        	$a = 1;
        	$b = "解封商户";
        }
    	$sql = "UPDATE qingka_wangke_user SET active = ? WHERE uid = ?";
    	$params = [$a, $uid];
    	$DB->prepare_query($sql, $params);
    	wlog($userrow['uid'],$b,"{$b}[UID {$uid}]成功",0);
    	jsonReturn(1,"操作成功");
	break;
   
   	case 'khqx':
	    $uid = $_POST['uid'];
	    $active = $_POST['khqx'];
	    if($userrow['uid'] != 1){
	       jsonReturn(-1,"无权限");
	    }
	    $row = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE uid = ?", [$uid]);
	    if($row['uuid'] != 1){
	       jsonReturn(-1,"只能给直属用户开通跨户权限");
	    }
        if($active == 1){
        	$a = 0;
        	$b = "关闭商户跨户充值权限";
        }else{
        	$a = 1;
        	$b = "开通商户跨户充值权限";
        }
    	$sql = "UPDATE qingka_wangke_user SET khqx = ? WHERE uid = ?";
    	$params = [$a, $uid];
    	$DB->prepare_query($sql, $params);
    	wlog($userrow['uid'],$b,"{$b}[UID {$uid}]成功",0);
    	jsonReturn(1,"{$b}成功");
	break;
//用户信息
	case 'userinfo':
	  if($islogin!=1){exit('{"code":-10,"msg":"请先登录"}');}
	  $a=$DB->get_row("select uid,user,notice from qingka_wangke_user where uid='{$userrow['uuid']}' ");
	  $dd=$DB->count("select count(oid) from qingka_wangke_order where uid='{$userrow['uid']}' ");
	  if($userrow['addprice']<0.1){
	  	 $DB->query("update qingka_wangke_user set addprice='1' where uid='{$userrow['uid']}' ");
	     jsonReturn(-9,"大佬，我得罪不起您啊，有什么做的不好的地方尽管提出来，我小本生意，经不起折腾，还望多多包涵");
	  }
	  if($userrow['uid']!=1){	  	
	  	if((int)$userrow['money']-(int)'0.1'>(int)$userrow['zcz']){
		  	$DB->query("update qingka_wangke_user set money='$zcz',active='0' where uid='{$userrow['uid']}' ");
		  	jsonReturn(-9,"账号异常，请联系你老大");
	    }
	  }
	     $djname=$DB->get_row("select name from qingka_wangke_dengji where rate='{$userrow['addprice']}' ");
	     $dlzs=$DB->count("select count(uid) from qingka_wangke_user where uuid='{$userrow['uid']}' ");
	     $dldl=$DB->count("select count(uid) from qingka_wangke_user where uuid='{$userrow['uid']}' and endtime>'$jtdate' ");
	  	 $dlzc=$DB->count("select count(uid) from qingka_wangke_user where uuid='{$userrow['uid']}' and addtime>'$jtdate' ");   
	  	 $jrjd=$DB->count("select count(uid) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime>'$jtdate' ");   
          $dailitongji=array(
               'dlzc'=>$dlzc,
               'dldl'=>$dldl,
               'dlxd'=>$dlxd,
               'dlzs'=>$dlzs,
               'jrjd'=>$jrjd
          );

	      $data=array(
	          'code'=>1,
	          'msg'=>'查询成功',
	          'name'=>$userrow['name'],
	          'uid'=>$userrow['uid'],
	          'user'=>$userrow['user'],
	          'money'=>round($userrow['money'],2),
	          'addprice'=>$userrow['addprice'],
	          'djname'=>$djname['name'],
	          'key'=>$userrow['key'],
	          'sjuser'=>$a['user'],
	          'dd'=>$dd,
	          'zcz'=>$userrow['zcz'],
	          'yqm'=>$userrow['yqm'],
	          'yqprice'=>$userrow['yqprice'],
	          'dailitongji'=>$dailitongji                  
	      );
	   exit(json_encode($data));
	break;
	case 'yqprice':
    $yqprice = trim(strip_tags(daddslashes($_POST['yqprice'])));
    $input_yqm = trim(strip_tags(daddslashes($_POST['yqm'])));

    if (!is_numeric($yqprice)) {
        jsonReturn(-1, "请正确输入费率，必须为数字");
    }

    if ($yqprice < $userrow['addprice']) {
        jsonReturn(-1, "下级默认费率不能比你低哦");
    }

    if ($yqprice < 0.2) {
        jsonReturn(-1, "邀请费率最低设置为0.25");
    }

    if ($yqprice * 100 % 5 != 0) {
        jsonReturn(-1, "邀请费率必须为0.05的倍数");
    }

    $has_input_yqm = ($input_yqm != "");
    $has_original_yqm = ($userrow['yqm'] != "");

    if ($has_input_yqm) {
        if (strlen($input_yqm) > 8) {
            jsonReturn(-1, "邀请码过长，不能超过8位");
        }

        if (!preg_match('/^[a-zA-Z0-9]+$/', $input_yqm)) {
            jsonReturn(-1, "邀请码只能包含数字和字母");
        }

        $sql = "SELECT uid FROM qingka_wangke_user WHERE yqm = ?";
        $result = $DB->prepare_getrow($sql, [$input_yqm]);
        if ($result) {
            jsonReturn(-1, "该邀请码已被使用，请更换其他邀请码");
        }

        $yqm = $input_yqm;
    } else {
        if ($has_original_yqm) {
            $yqm = $userrow['yqm'];
        } else {
            $yqm = random(5, 5);
            $sql = "SELECT uid FROM qingka_wangke_user WHERE yqm = ?";
            $result = $DB->prepare_getrow($sql, [$yqm]);
            if ($result) {
                $yqm = random(6, 5);
            }
        }
    }

    if (!$has_original_yqm && !$has_input_yqm) {
        $deduct = 1;
    } elseif ($has_original_yqm && !$has_input_yqm) {
        $deduct = 0;
    } elseif ($has_original_yqm && $has_input_yqm) {
        $deduct = 5;
    } else {
        $deduct = 6;
    }

    if ($userrow['money'] < $deduct) {
        jsonReturn(-1, "余额不足{$deduct}元，无法操作");
    }

    $update_sql = "UPDATE qingka_wangke_user SET yqprice = ?";
    $params = [$yqprice];
    if ($yqm != $userrow['yqm']) {
        $update_sql .= ", yqm = ?";
        $params[] = $yqm;
    }
    if ($deduct > 0) {
        $update_sql .= ", money = money - ?";
        $params[] = $deduct;
    }
    $update_sql .= " WHERE uid = ?";
    $params[] = $userrow['uid'];

    $DB->prepare_query($update_sql, $params);

    $action = ($userrow['yqm'] == "") ? "开通" : "修改";
    wlog($userrow['uid'], "{$action}邀请码", "用户{$action}邀请码（{$yqm}）成功!扣费{$deduct}积分", "-{$deduct}");

    if ($deduct == 0) {
        jsonReturn(1, "设置成功");
    } else {
        jsonReturn(1, "{$action}成功! 扣除 {$deduct} 积分");
    }
    break;
        case 'ktapi':
        $type = trim(strip_tags(daddslashes($_GET['type'])));
        $uid = trim(strip_tags(daddslashes($_GET['uid'])));
        $key = random(32);
        $key_md5 = md5($key);
        if ($type == 1) {
            if ($userrow['money'] < 200) {
                if ($userrow['money'] >= 10) {
                    $sql = "update qingka_wangke_user set `key`=?, `money`=`money`-10 where uid=?";
                    $params = [$key_md5, $userrow['uid']];
                    $DB->prepare_query($sql, $params);

                    wlog($userrow['uid'], "开通接口", "开通接口成功!扣费10积分", '-10');
                    exit(json_encode(['code' => 1, 'msg' => "花费10积分开通接口成功", 'key' => $key]));
                } else {
                    exit(json_encode(['code' => -1, 'msg' => "余额不足"]));
                }
            } else {
                $sql = "update qingka_wangke_user set `key`=? where uid=?";
                $params = [$key_md5, $userrow['uid']];
                $DB->prepare_query($sql, $params);

                wlog($userrow['uid'], "开通接口", "免费开通接口成功!", '0');
                exit(json_encode(['code' => 1, 'msg' => "免费开通成功", 'key' => $key]));
            }
        } elseif ($type == 2) {
            if ($userrow['money'] < 5) {
                wlog($userrow['uid'], "开通接口", "尝试给下级UID{$uid}开通接口失败! 原因：余额不足", '0');
                exit(json_encode(['code' => -2, 'msg' => "余额不足以开通"]));
            } else {
                if (empty($uid)) {
                    exit(json_encode(['code' => -2, 'msg' => "uid不能为空"]));
                }

                $sql1 = "update qingka_wangke_user set `key`=? where uid=?";
                $params1 = [$key_md5, $uid];
                $DB->prepare_query($sql1, $params1);

                $sql2 = "update qingka_wangke_user set `money`=`money`-5 where uid=?";
                $params2 = [$userrow['uid']];
                $DB->prepare_query($sql2, $params2);

                wlog($userrow['uid'], "开通接口", "给下级代理UID{$uid}开通接口成功!扣费5积分", '-5');
                wlog($uid, "开通接口", "你上级给你开通API接口成功!", '0');
                exit(json_encode(['code' => 1, 'msg' => "花费5积分开通成功"]));
            }
        } elseif ($type == 3) {
            if ($userrow['key'] == "0") {
                exit(json_encode(['code' => -1, 'msg' => "请先开通key"]));
            } elseif ($userrow['key'] != "") {
                $sql = "update qingka_wangke_user set `key`=? where uid=?";
                $params = [$key_md5, $userrow['uid']];
                $DB->prepare_query($sql, $params);

                wlog($userrow['uid'], "开通接口", "更换接口成功", '0');
                exit(json_encode(['code' => 1, 'msg' => "更换成功", 'key' => $key]));
            }
        } elseif ($type == 4) {
            if ($userrow['key'] == "0") {
                exit(json_encode(['code' => -1, 'msg' => "请先开通key"]));
            } elseif ($userrow['key'] != "") {
                $sql = "update qingka_wangke_user set `key`='0' where uid=?";
                $params = [$userrow['uid']];
                $DB->prepare_query($sql, $params);
                wlog($userrow['uid'], "关闭接口", "关闭接口成功", '0');
                exit(json_encode(['code' => 1, 'msg' => "已关闭"]));
            }
        }
        exit(json_encode(['code' => -2, 'msg' => "未知异常"]));
        break;
        case 'get_user_ips':
    $uid = $userrow['uid'];
    $result = $DB->query("
        SELECT ip, MAX(addtime) AS last_addtime, GROUP_CONCAT(type SEPARATOR ', ') AS types
        FROM qingka_wangke_log
        WHERE uid = '{$uid}'
        GROUP BY ip
        ORDER BY last_addtime DESC
        LIMIT 9
    ");
    $ips = array();
    while ($row = $DB->fetch($result)) {
        $type = $row['types'];
        if (mb_strlen($type, 'UTF-8') > 25) {
            $type = mb_substr($type, 0, 24, 'UTF-8') . '';
        }
        $ips[] = array(
            'ip' => $row['ip'],
            '最后使用时间' => $row['last_addtime'],
            '操作内容' => $type
        );
    }
    exit(json_encode(array("code" => 1, "ips" => $ips)));
break;
	case 'passwd':
      $oldpass=trim(strip_tags(daddslashes($_POST['oldpass'])));
      $newpass=trim(strip_tags(daddslashes($_POST['newpass'])));  
		if($oldpass!=$userrow['pass']) {
          exit('{"code":-1,"msg":"原密码错误"}');
		}
		if($newpass==''){
			exit('{"code":-1,"msg":"新密码不能为空"}');
		}
		$sql="update `qingka_wangke_user` set `pass` ='{$newpass}' where `uid`='{$userrow['uid']}'";
		if($DB->query($sql)){
			exit('{"code":1,"msg":"修改成功,请牢记密码"}');
          }else{
            exit('{"code":-1,"msg":"修改失败"}');
	      }
    break;
//充值模块
    case 'pay':
        $zdpay = $conf['zdpay'];
        $money = trim(strip_tags($_POST['money']));
        $name = "零食购买-" . $money . "";
        if (!preg_match('/^[0-9.]+$/', $money)) exit('{"code":-1,"msg":"订单金额不合法"}');
        if ($money < $zdpay) {
            jsonReturn(-1, "在线充值最低{$zdpay}积分");
        }
        $sql = "select * from qingka_wangke_user where uid=?";
        $params = [$userrow['uuid']];
        $row = $DB->prepare_getrow($sql, $params);
        if ($row['uid'] == '1') {
            $out_trade_no = date("YmdHis") . rand(111, 999);
            $wz = $_SERVER['HTTP_HOST'];
            $sql = "insert into `qingka_wangke_pay` (`out_trade_no`,`uid`,`num`,`name`,`money`,`ip`,`addtime`,`domain`,`status`) values (?,?,?,?,?,?,?,?,?)";
            $params = [$out_trade_no, $userrow['uid'], $money, $name, $money, $clientip, $date, $wz, '0'];
            if ($DB->prepare_query($sql, $params)) {
                exit('{"code":1,"msg":"生成订单成功！","out_trade_no":"' . $out_trade_no . '","need":"' . $money . '"}');
            } else {
                exit('{"code":-1,"msg":"生成订单失败！"}');
            }
        } else {
            jsonReturn(-1, "请您根据上面的信息联系上家充值。");
        }
        break;
    
	case 'paylist':
	    $page=trim(daddslashes($_GET['page']));
	    $limit=trim(daddslashes($_GET['limit']));
	    $pageu = ($page - 1) * $limit;//当前界面	
	    $a=$DB->query("select * from qingka_wangke_pay order by oid desc limit $pageu,$limit");
	    $count=$DB->count("select count(*) from qingka_wangke_pay");
	    while($row=$DB->fetch($a)){
	        if($row['status']==0){
	            $row['status']='未支付';
	        }elseif($row['status']==1){
	            $row['status']='已支付';
	        }
	        if($row['type']=="alipay"){
	            $row['type']="支付宝";
	        }elseif($row['type']="vxpay"){
	            $row['type']="微信";
	        }elseif($row['type']="qqpay"){
	            $row['type']="QQ";
	        }
	        if($row['endtime']==''){
	            $row['endtime']="支付未完成";
	        }
	   	   $data[]=array(
	   	        'oid'=>$row['oid'],
   	            'out_trade_no'=>$row['out_trade_no'],
	   	        'addtime'=>$row['addtime'],
	   	        'type'=>$row['type'],
	   	        'uid'=>$row['uid'],
	   	        'endtime'=>$row['endtime'],
	   	        'name'=>$row['name'],
	   	        'money'=>$row['money'],
	   	        'status'=>$row['status'],
	   	        'ip'=>$row['ip'],
	   	   );
	    }
	    
	    //array_multisort($sort, SORT_ASC, $rate, SORT_ASC, $data);
	    $data=array('code'=>1,'data'=>$data,"count"=>$count);
	    exit(json_encode($data));
	break;

//质押系统
case 'zhiyalist':
    $page = isset($post_data['page']) ? intval($post_data['page']) : 1;
    $limit = isset($post_data['limit']) ? intval($post_data['limit']) : 10;
    $need_page = isset($post_data['need_page']) ? intval($post_data['need_page']) : 1;

    $where = " WHERE 1=1";
    if(isset($post_data['status'])) {
        $status = intval($post_data['status']);
        $where .= " AND a.status='$status'";
    }

    $total = $DB->count("SELECT COUNT(*) FROM qingka_wangke_zhiya_config a $where");

    $data = [];
    if($need_page) {
        $offset = ($page - 1) * $limit;
        $limit_sql = "LIMIT $offset,$limit";
    } else {
        $limit_sql = "";
    }

    $query = $DB->query("SELECT a.*,b.name as category_name 
                        FROM qingka_wangke_zhiya_config a 
                        LEFT JOIN qingka_wangke_fenlei b ON b.id=a.category_id 
                        $where 
                        ORDER BY a.id DESC $limit_sql");
    while($row = $DB->fetch($query)){
        $data[] = $row;
    }

    $result = [
        'code' => 1,
        'data' => $data,
    ];

    if($need_page) {
        $result['current_page'] = intval($page);
        $result['last_page'] = ceil($total / $limit);
        $result['total'] = intval($total);
        $result['per_page'] = intval($limit);
    }

    exit(json_encode($result));
break;

case 'zhiya_config':
    if ($userrow['uid'] != 1) {
        jsonReturn(-1, "无权限");
    }
    $data = $post_data['data'];
    $active = trim($post_data['active']);
    $category_id = intval($data['category_id']);
    $amount = floatval($data['amount']);
    $discount_rate = floatval($data['discount_rate']);
    $days = floatval($data['days']);
    $id = intval($data['id']);

    if ($active == '1') {
        $sql = "INSERT INTO qingka_wangke_zhiya_config (category_id, amount, discount_rate, days, status, addtime) VALUES (?, ?, ?, ?, '1', NOW())";
        $params = [$category_id, $amount, $discount_rate, $days];
        $result = $DB->prepare_query($sql, $params);
        if($result){
            wlog($userrow['uid'], '添加质押', "管理员添加质押配置，金额{$amount}元", 0);
            jsonReturn(1, "添加成功");
        }else{
            jsonReturn(-1, "添加失败");
        }
    } elseif ($active == '2') {
        $sql = "UPDATE qingka_wangke_zhiya_config SET category_id=?, amount=?, discount_rate=?, days=? WHERE id=?";
        $params = [$category_id, $amount, $discount_rate, $days, $id];
        $result = $DB->prepare_query($sql, $params);
         if($result){
            wlog($userrow['uid'], '修改质押', "管理员修改质押配置，金额{$amount}元", 0);
            jsonReturn(1, "修改成功");
        }else{
            jsonReturn(-1, "修改失败");
        }
    } elseif ($active == '3') {
        $sql = "SELECT COUNT(*) FROM qingka_wangke_zhiya_records WHERE config_id=? AND status=1";
        $params = [$id];
        $count = $DB->prepare_count($sql, $params);

        if ($count > 0) {
            jsonReturn(-1, "该配置下有正在使用的质押记录，无法删除");
        }

        $sql = "DELETE FROM qingka_wangke_zhiya_config WHERE id=?";
        $params = [$id];
        $result = $DB->prepare_query($sql, $params);
         if($result){
            wlog($userrow['uid'], '删除质押', "管理员删除质押配置", 0);
            jsonReturn(1, "删除成功");
        }else{
            jsonReturn(-1, "删除失败");
        }
    } elseif ($active == '4') {
        $status = intval($data['status']);

        if ($status == 0) {
            $sql = "SELECT COUNT(*) FROM qingka_wangke_zhiya_records WHERE config_id=? AND status=1";
            $params = [$id];
            $count = $DB->prepare_count($sql, $params);

            if ($count > 0) {
                jsonReturn(-1, "该配置下有正在使用的质押记录，无法禁用");
            }
        }

        $sql = "UPDATE qingka_wangke_zhiya_config SET status=? WHERE id=?";
        $params = [$status, $id];
        $result = $DB->prepare_query($sql, $params);
         if($result){
            wlog($userrow['uid'], '质押状态', "管理员更新质押配置状态", 0);
            jsonReturn(1, "状态更新成功");
        }else{
            jsonReturn(-1, "状态更新失败");
        }
    }
    break;

case 'user_zhiya':
    $config_id = intval($post_data['config_id']);
    $exists = $DB->get_row("SELECT * FROM qingka_wangke_zhiya_records 
                            WHERE uid='{$userrow['uid']}' AND config_id='$config_id' AND status=1");
    if($exists) {
        jsonReturn(-1, "您已经质押过该配置且尚未到期，请勿重复质押");
    }
    $config = $DB->get_row("SELECT * FROM qingka_wangke_zhiya_config WHERE 
                        id='$config_id' AND status=1");
    if(!$config) {
        jsonReturn(-1,"质押配置不存在");
    }
    if($userrow['money'] < $config['amount']) {
        jsonReturn(-1,"余额不足");
    }
    $DB->query('BEGIN');
    try {
        $DB->query("UPDATE qingka_wangke_user SET money=money-{$config['amount']} WHERE uid='{$userrow['uid']}'");
        $addtime = date('Y-m-d H:i:s');
        $datetime = new DateTime($addtime);
        $datetime->modify("+" . $config['days'] . " days");
        $endtime = $datetime->format('Y-m-d H:i:s');
        $DB->query("INSERT INTO qingka_wangke_zhiya_records (uid,config_id,status,money,addtime,endtime) 
                    VALUES ('{$userrow['uid']}','{$config['id']}','1',{$config['amount']},'$addtime','$endtime')");
        wlog($userrow['uid'], '用户质押', "用户进行质押，扣除{$config['amount']}元", -$config['amount']);
        $DB->query('COMMIT');
        jsonReturn(1,"质押成功");
    } catch (Exception $e) {
        $DB->query('ROLLBACK'); 
        jsonReturn(-1, "质押失败，请重试");
    }
break;

case 'my_zhiya':
    $page = isset($post_data['page']) ? intval($post_data['page']) : 1;
    $limit = isset($post_data['limit']) ? intval($post_data['limit']) : 10;
    $offset = ($page - 1) * $limit;
    $total = $DB->count("SELECT COUNT(*) FROM qingka_wangke_zhiya_records WHERE uid='{$userrow['uid']}'");
    $data = [];
    $query = $DB->query("SELECT a.*,c.name as category_name,b.category_id,b.amount,b.discount_rate,b.days 
                        FROM qingka_wangke_zhiya_records a 
                        LEFT JOIN qingka_wangke_zhiya_config b ON b.id=a.config_id 
                        LEFT JOIN qingka_wangke_fenlei c ON c.id=b.category_id 
                        WHERE a.uid='{$userrow['uid']}'
                        ORDER BY a.id DESC LIMIT $offset,$limit");
    while($row = $DB->fetch($query)){
        $row['status'] = intval($row['status']); 
        $data[] = $row;
    }
    exit(json_encode([
        'code' => 1,
        'data' => $data,
        'current_page' => intval($page),
        'last_page' => ceil($total / $limit),
        'total' => intval($total),
        'per_page' => intval($limit)
    ]));
break;

case 'my_zhiya_del':
    $record_id = intval($post_data['record_id']);
    $record = $DB->get_row("SELECT * FROM qingka_wangke_zhiya_records WHERE id='$record_id'");
    if(!$record){
        jsonReturn(-1, "无质押记录");
    }
    if($userrow['uid'] != $record['uid']){
        jsonReturn(-1, "兄弟，你是谁啊");
    }
    $current_time = time();
    $addtime = strtotime($record['addtime']);
    $endtime = $record['endtime'] ? strtotime($record['endtime']) : null;
    if($endtime && $current_time < $endtime){
        jsonReturn(-1, "质押时间未结束，无法解押");
    }
    $DB->query("UPDATE qingka_wangke_user SET money=money+{$record['money']} WHERE uid='{$record['uid']}'");
    $DB->query("DELETE FROM qingka_wangke_zhiya_records WHERE id='$record_id'");
    wlog($record['uid'], '取消质押', "用户手动取消项目质押，退还金额{$record['money']}元", $record['money']);
    jsonReturn(1, "手动退押成功，已退还{$record['money']}元余额");
break;

case 'my_zhiya_addtime':
    $record_id = intval($post_data['record_id']);
    $adddate = intval($post_data['date']);
    $record = $DB->get_row("SELECT * FROM qingka_wangke_zhiya_records WHERE id='$record_id'");
    if (!$record) {
        jsonReturn(-1, "无质押记录");
    }
    if ($userrow['uid'] != $record['uid']) {
        jsonReturn(-1, "兄弟，你是谁啊");
    }
    if ($adddate <= 0) {
        jsonReturn(-1, "增加的天数必须大于0");
    }
    if (empty($record['endtime'])) {
        jsonReturn(-1, "获取结束日期错误");
    }
    
    $endtime = strtotime($record['endtime']);
    if ($endtime === false) {
        jsonReturn(-1, "结束日期格式错误");
    }
    $current_time = time();
    $new_endtime = $endtime + ($adddate * 24 * 60 * 60);
    $new_endtime_str = date('Y-m-d H:i:s', $new_endtime);
    $updates = "endtime='$new_endtime_str'";
    if ($new_endtime > $current_time) {
        $updates .= ", status=1";
    }
    $DB->query("UPDATE qingka_wangke_zhiya_records SET $updates WHERE id='$record_id'");
    wlog($record['uid'], '增加质押时间', "用户确认增加质押时间{$adddate}天，解押时间{$new_endtime_str}", $record['money']);
    jsonReturn(1, "成功增加质押时间{$adddate}天，解押时间{$new_endtime_str}");
    break;

case 'get_zhiya_users':
    if ($userrow['uid'] != 1) {
        jsonReturn(-1, "无权限");
    }
    $config_id = intval($post_data['config_id']);
    $data = [];
    $query = $DB->query("SELECT a.*,b.user,c.days 
                        FROM qingka_wangke_zhiya_records a 
                        LEFT JOIN qingka_wangke_user b ON b.uid=a.uid 
                        LEFT JOIN qingka_wangke_zhiya_config c ON c.id=a.config_id 
                        WHERE a.config_id='$config_id' 
                        ORDER BY a.id DESC");
    while($row = $DB->fetch($query)){
        $data[] = $row;
    }
    exit(json_encode([
        'code' => 1,
        'data' => $data
    ]));
break;

case 'cancel_zhiya_record':
    if($userrow['uid'] != 1){
        jsonReturn(-1, "无权限");
    }
    $record_id = intval($post_data['record_id']);
        $record = $DB->get_row("SELECT * FROM qingka_wangke_zhiya_records WHERE id='$record_id'");
        if(!$record){
            jsonReturn(-1, "无质押记录");
        }
        if($record['status'] == 2){
            jsonReturn(-1, "该质押已到期或已取消");
        }
        $DB->query("UPDATE qingka_wangke_user SET money=money+{$record['money']} WHERE uid='{$record['uid']}'");
        $DB->query("DELETE FROM qingka_wangke_zhiya_records WHERE id='$record_id'");
        wlog($record['uid'], '取消质押', "管理员取消项目质押，退还金额{$record['money']}元", $record['money']);
        jsonReturn(1, "取消质押成功，已退还{$record['money']}元");
break;

case 'get_categories':
    if ($userrow['uid'] != 1) {
        jsonReturn(-1, "无权限");
    }
		$query = $DB->query("SELECT id,name FROM qingka_wangke_fenlei WHERE status=1 ORDER BY sort ASC");
		$data = array();
		while($row = $DB->fetch($query)) {
			$data[] = $row;
		}
		exit(json_encode([
			'code' => 1,
			'msg' => 'success',
			'data' => $data
		]));
	break;
    
//日志系统
case 'loglist':
    $page = trim(strip_tags(daddslashes(trim($_POST['page']))));
    $type = trim(strip_tags(daddslashes(trim($_POST['type']))));
    $types = trim(strip_tags(daddslashes(trim($_POST['types']))));
    $qq = trim(strip_tags(daddslashes(trim($_POST['qq']))));
    $pagesize = 20;
    $pageu = ($page - 1) * $pagesize;
    $sql_conditions = "1=1";
    $params = [];
    if ($userrow['uid'] != '1') {
        $sql_conditions .= " AND uid=?";
        $params[] = $userrow['uid'];
    }
    if ($type != '') {
        $sql_conditions .= " AND type LIKE ?";
        $params[] = "%{$type}%";
    }
    if ($types != '') {
        if ($types == '1') {
            $sql_conditions .= " AND uid=?";
            $params[] = $qq;
        } elseif ($types == '2') {
            $sql_conditions .= " AND text LIKE ?";
            $params[] = "%{$qq}%";
        } elseif ($types == '3') {
            $sql_conditions .= " AND money LIKE ?";
            $params[] = "%{$qq}%";
        } elseif ($types == '4') {
            $sql_conditions .= " AND addtime=?";
            $params[] = $qq;
        }
    }
    $sql = "SELECT * FROM qingka_wangke_log WHERE {$sql_conditions} ORDER BY id DESC LIMIT ?, ?";
    $params[] = $pageu;
    $params[] = $pagesize;

    $stmt = $DB->prepare_query($sql, $params);
    if ($stmt === false) {
        exit(json_encode(['code' => 0, 'msg' => 'Database query failed']));
    }

    $result = $stmt->get_result();
    $data = [];
    while ($row = $DB->fetch($result)) {
        $data[] = $row;
    }
    $stmt->close();
    $count_sql = "SELECT COUNT(*) FROM qingka_wangke_log WHERE {$sql_conditions}";
   $count = $DB->prepare_count($count_sql, array_slice($params, 0, -2)); // 移除 LIMIT 参数
    $last_page = ceil($count / $pagesize);
    $response = [
        'code' => 1,
        'data' => $data,
        'current_page' => (int)$page,
        'last_page' => $last_page
    ];
    exit(json_encode($response));
break;

//推送设置
case 'saveUserSetting':
    $settings = [
        'login_notification' => intval($_POST['login_notification'] ?? 0),
        'work_order_notification' => intval($_POST['work_order_notification'] ?? 0),
        'announcement_notification' => intval($_POST['announcement_notification'] ?? 0),
        'balance_warning' => intval($_POST['balance_warning'] ?? 0),
        'order_completion_notification' => intval($_POST['order_completion_notification'] ?? 0),
        'supply_balance_warning' => intval($_POST['supply_balance_warning'] ?? 0),
        'supply_update_notification' => intval($_POST['supply_update_notification'] ?? 0)
    ];
   
    $settings_json = json_encode($settings);
   
    $sql = "UPDATE `qingka_wangke_user` SET `user_settings`=? WHERE `uid`=?";
    $result = $DB->prepare_query($sql, [$settings_json, $userrow['uid']]);
   
    if ($result) {
        exit('{"code":1,"msg":"用户设置保存成功"}');
    } else {
        exit('{"code":-1,"msg":"用户设置保存失败"}');
    }
    break;
    
case 'getUserSetting':
    $sql = "SELECT `user_settings` FROM `qingka_wangke_user` WHERE `uid`=?";
    $result = $DB->prepare_getrow($sql, [$userrow['uid']]);
    if ($result) {
        $settings = json_decode($result['user_settings'], true);
        if (!$settings) {
            $settings = [
                'login_notification' => 0,
                'work_order_notification' => 0,
                'announcement_notification' => 0,
                'balance_warning' => 0,
                'order_completion_notification' => 0,
                'supply_balance_warning' => 0,
                'supply_update_notification' => 0
            ];
        }
       
        exit(json_encode([
            'code' => 1,
            'settings' => $settings
        ]));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '获取用户设置失败']));
    }
    break;
        case 'savePushToken':
        $token = trim($_GET['token']);
        $sql = "UPDATE `qingka_wangke_user` SET `tuisongtoken`=? WHERE `uid`=?";
        $result = $DB->prepare_query($sql, [$token, $userrow['uid']]);
        if ($result) {
            exit('{"code":1,"msg":"推送Token保存成功"}');
        } else {
            exit('{"code":-1,"msg":"推送Token保存失败"}');
        }
        break;

    case 'getPushToken':
        $sql = "SELECT `tuisongtoken` FROM `qingka_wangke_user` WHERE `uid`=?";
        $token = $DB->prepare_getrow($sql, [$userrow['uid']]);
        if ($token) {
            exit(json_encode(['code' => 1, 'token' => $token['tuisongtoken']]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '暂未设置推送token']));
        }
        break;
        
        case 'testPushToken':
        tuisong($userrow['uid'], "test", "推送测试", "这是一条测试信息");
        exit('{"code":1,"msg":"已发送测试信息，请查收"}');
        break;
//上级迁移
        case 'sjqy':
        $uuid = daddslashes($_POST['uid']);
        $yqm = daddslashes($_POST['yqm']);
        if (empty($uuid) || empty($yqm)) {
            exit('{"code":0,"msg":"所有项目不能为空"}');
        }
        if ($conf['sjqykg'] == 0) {
            exit('{"code":0,"msg":"管理员未打开迁移功能"}');
        } elseif ($conf['sjqykg'] == 1) {
            $sql = "SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1";
            $params = [$uuid];
            $row = $DB->prepare_getrow($sql, $params);
            if ($row) {
                if ($yqm == $row['yqm']) {
                    $sql1 = "SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1";
                    $params1 = [$userrow['uid']];
                    $row1 = $DB->prepare_getrow($sql1, $params1);

                    if ($row1['uuid'] != $uuid) {
                        if ($row1['uid'] != $uuid) {
                            $sql_update = "UPDATE qingka_wangke_user SET `uuid` = ? WHERE uid = ?";
                            $params_update = [$uuid, $userrow['uid']];
                            $result_update = $DB->prepare_query($sql_update, $params_update);

                            if ($result_update) {
                                jsonReturn(1, "迁移成功,您已迁移至[UID$uuid]的名下");
                            } else {
                                jsonReturn(-1, "迁移失败,未知错误");
                            }
                        } else {
                            jsonReturn(-1, "禁止填写自己的UID");
                        }
                    } else {
                        jsonReturn(-1, "该用户已经是你的上级了");
                    }
                } else {
                    jsonReturn(-1, "非该用户邀请码，请重新输入");
                }
            } else {
                jsonReturn(-1, "UID不存在，请重新输入");
            }
        }
        break;
}

?>